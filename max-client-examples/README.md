# Max Bot API Client library for Python


## Документация

В [документации](https://github.com/max-messenger/max-bot-api-client-py/tree/master/docs) вы можете найти подробные инструкции по использованию фреймворка.

## Быстрый старт

> Если вы новичок, то можете прочитать [официальную документацию](https://dev.max.ru/), написанную разработчиками Max

### Получение токена
Откройте диалог с [MasterBot](https://max.ru/MasterBot), следуйте инструкциям и создайте нового бота. После создания бота MasterBot отправит вам токен.
