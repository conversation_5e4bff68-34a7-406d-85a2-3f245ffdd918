# PhotoAttachmentRequestPayload

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**url** | **str** | Any external image URL you want to attach | [optional] 
**token** | **str** | Token of any existing attachment | [optional] 
**photos** | [**dict(str, PhotoToken)**](PhotoToken.md) | Tokens were obtained after uploading images | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


