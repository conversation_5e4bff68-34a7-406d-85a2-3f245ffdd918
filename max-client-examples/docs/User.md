# User

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**user_id** | **int** | Users identifier | 
**first_name** | **str** | Users first name | 
**last_name** | **str** | Users last name | 
**username** | **str** | Unique public user name. Can be &#x60;null&#x60; if user is not accessible or it is not set | 
**is_bot** | **bool** | &#x60;true&#x60; if user is bot | 
**last_activity_time** | **int** | Time of last user activity in Max (Unix timestamp in milliseconds). Can be outdated if user disabled its \&quot;online\&quot; status in settings | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


