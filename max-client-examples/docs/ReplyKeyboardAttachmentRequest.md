# ReplyKeyboardAttachmentRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**direct** | **bool** | Applicable only for chats. If &#x60;true&#x60; keyboard will be shown only for user bot mentioned or replied | [optional] [default to False]
**direct_user_id** | **int** | If set to &#x60;true&#x60;, reply keyboard will only be shown to this participant in chat | [optional] 
**buttons** | **list[list[ReplyButton]]** | Two-dimensional array of buttons | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


