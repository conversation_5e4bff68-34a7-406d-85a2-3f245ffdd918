# UserRemovedFromChatUpdate

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**chat_id** | **int** | Chat identifier where event has occurred | 
**user** | [**User**](User.md) | User removed from chat | 
**admin_id** | **int** | Administrator who removed user from chat. Can be &#x60;null&#x60; in case when user left chat | [optional] 
**is_channel** | **bool** | Indicates whether user has been removed from channel or not | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


