# UserAddedToChatUpdate

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**chat_id** | **int** | Chat identifier where event has occurred | 
**user** | [**User**](User.md) | User added to chat | 
**inviter_id** | **int** | User who added user to chat. Can be &#x60;null&#x60; in case when user joined chat by link | [optional] 
**is_channel** | **bool** | Indicates whether user has been added to channel or not | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


