# ChatPatch

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**icon** | [**PhotoAttachmentRequestPayload**](PhotoAttachmentRequestPayload.md) |  | [optional] 
**title** | **str** |  | [optional] 
**pin** | **str** | Identifier of message to be pinned in chat. In case you want to remove pin, use [unpin](#operation/unpinMessage) method | [optional] 
**notify** | **bool** | By default, participants will be notified about change with system message in chat/channel | [optional] [default to True]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


