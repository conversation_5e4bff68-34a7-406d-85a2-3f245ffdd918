# ChatButton

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**chat_title** | **str** | Title of chat to be created | 
**chat_description** | **str** | Chat description | [optional] 
**start_payload** | **str** | Start payload will be sent to bot as soon as chat created | [optional] 
**uuid** | **int** | Unique button identifier across all chat buttons in keyboard. If &#x60;uuid&#x60; changed, new chat will be created on the next click. Server will generate it at the time when button initially posted. Reuse it when you edit the message.&#39; | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


