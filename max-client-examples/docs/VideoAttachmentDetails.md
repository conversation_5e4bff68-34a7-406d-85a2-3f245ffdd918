# VideoAttachmentDetails

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**token** | **str** | Video attachment token | 
**urls** | [**VideoUrls**](VideoUrls.md) | URLs to download or play video. Can be null if video is unavailable | [optional] 
**thumbnail** | [**PhotoAttachmentPayload**](PhotoAttachmentPayload.md) | Video thumbnail | [optional] 
**width** | **int** | Video width | 
**height** | **int** | Video height | 
**duration** | **int** | Video duration in seconds | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


