#!/usr/bin/env python3
"""
Скрипт для добавления тестовых данных
"""

import asyncio
from datetime import date, time, timedelta
from database import init_db, close_db, clear_appointments, add_appointment_to_db

async def add_test_appointments():
    """Добавление тестовых записей"""
    await init_db()
    
    # Очищаем старые данные
    cleared = await clear_appointments()
    print(f"🗑️ Удалено {cleared} старых записей")
    
    # Тестовые данные
    base_date = date.today() + timedelta(days=1)
    
    services = [
        "Урок_английского",
        "Консультация_врача", 
        "Массаж",
        "Стрижка",
        "Мани<PERSON><PERSON><PERSON>"
    ]
    
    times = [
        time(9, 0),   # 09:00:00
        time(10, 0),  # 10:00:00
        time(11, 0),  # 11:00:00
        time(14, 0),  # 14:00:00
        time(15, 0),  # 15:00:00
        time(16, 0),  # 16:00:00
    ]
    
    # Генерируем записи на 5 дней
    added_count = 0
    for day_offset in range(5):
        current_date = base_date + timedelta(days=day_offset)
        date_str = current_date.strftime('%Y-%m-%d')
        
        for service in services:
            for appointment_time in times:
                appt_id = await add_appointment_to_db(service, current_date, appointment_time)
                if appt_id:
                    added_count += 1
                    print(f"✅ Добавлена запись ID {appt_id}: {service} на {current_date} в {appointment_time}")
    
    print(f"\n🎉 Добавлено {added_count} тестовых записей!")
    await close_db()

if __name__ == "__main__":
    asyncio.run(add_test_appointments())
