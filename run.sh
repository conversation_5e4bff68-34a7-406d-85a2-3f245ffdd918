#!/bin/bash

# Скрипт для запуска FastAPI бота с webhook

echo "🚀 Запуск Max Bot FastAPI с Webhook"
echo "=================================="

# Проверяем виртуальное окружение
if [ ! -d "venv" ]; then
    echo "❌ Виртуальное окружение не найдено!"
    exit 1
fi

# Активируем виртуальное окружение
source venv/bin/activate

echo "📦 Установка зависимостей..."
pip install -r requirements.txt

echo "🗄️ Добавление тестовых данных..."
python add_test_data.py add

# Определяем порт (по умолчанию 8000)
PORT=${1:-8000}

echo "🔍 Поиск ngrok туннеля..."
python get_ngrok_url.py

echo ""
echo "💡 Если ngrok не найден, запустите:"
echo "   ngrok http $PORT"
echo ""
echo "🔧 Для смены webhook URL используйте:"
echo "   python set_webhook_url.py <новый_домен>"
echo "   python get_ngrok_url.py auto  # автоматически"
echo ""

echo "🌐 Запуск FastAPI сервера на порту $PORT..."
echo "🔍 Health check: http://localhost:$PORT/health"
echo ""
echo "Для остановки нажмите Ctrl+C"
echo ""

# Запускаем сервер
python main.py $PORT
