import os
import time
import json
import max_client
from max_client.rest import ApiException
from dotenv import load_dotenv
from max_client.models.message_created_update import MessageCreatedUpdate
from max_client.models.message_callback_update import Message<PERSON>allbackUpdate
from max_client.models.update import Update
from max_client.models.callback_button import CallbackButton
from max_client.models.inline_keyboard_attachment_request import InlineKeyboardAttachmentRequest
from max_client.models.inline_keyboard_attachment_request_payload import Inline<PERSON>eyboardAttachmentRequestPayload

load_dotenv()

# Configure API key authorization: access_token
configuration = max_client.Configuration()
configuration.api_key['access_token'] = os.getenv('MAX_BOT_TOKEN')

# Create API instances
subscriptions_api = max_client.SubscriptionsApi(max_client.ApiClient(configuration))
messages_api = max_client.MessagesApi(max_client.ApiClient(configuration))

import psycopg2
from psycopg2 import Error, errors

# Database connection parameters
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_NAME = os.getenv("DB_NAME", "max_ai_bot")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")
DB_PORT = os.getenv("DB_PORT", "5432")
ADMIN_ID = os.getenv("ADMIN_ID")

def create_appointments_table():
    conn = None
    try:
        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()
        cur.execute("""
            CREATE TABLE IF NOT EXISTS appointments (
                id SERIAL PRIMARY KEY,
                service_name VARCHAR(255) NOT NULL,
                appointment_date DATE NOT NULL,
                appointment_time TIME NOT NULL,
                is_booked BOOLEAN DEFAULT FALSE,
                booked_by_user_id TEXT,
                booked_by_user_name TEXT
            );
        """)
        conn.commit()
        print("Table 'appointments' ensured to exist.")
    except Error as e:
        print(f"Error creating table: {e}")
        if "permission denied for schema public" in str(e):
            print("Attempting to grant CREATE permission on public schema...")
            conn.rollback() # Rollback the aborted transaction
            try:
                cur.execute("GRANT CREATE ON SCHEMA public TO CURRENT_USER;")
                conn.commit()
                print("CREATE permission granted. Retrying table creation...")
                # Retry table creation after granting permission
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS appointments (
                        id SERIAL PRIMARY KEY,
                        service_name VARCHAR(255) NOT NULL,
                        appointment_date DATE NOT NULL,
                        appointment_time TIME NOT NULL,
                        is_booked BOOLEAN DEFAULT FALSE,
                        booked_by_user_id TEXT,
                        booked_by_user_name TEXT
                    );
                """)
                conn.commit()
                print("Table 'appointments' ensured to exist after granting permissions.")
            except Error as grant_e:
                print(f"Failed to grant CREATE permission: {grant_e}")
    finally:
        if conn:
            cur.close()
            conn.close()

def load_appointments_from_db():
    conn = None
    appointments = []
    try:
        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()
        cur.execute("SELECT id, service_name, appointment_date, appointment_time, is_booked FROM appointments ORDER BY appointment_date, appointment_time;")
        rows = cur.fetchall()
        for row in rows:
            appointments.append({
                'id': row[0],
                'service_name': row[1],
                'date': row[2].strftime('%Y-%m-%d'), # Format date as string
                'time': str(row[3]), # Time object to string
                'is_booked': row[4]
            })
        print("Appointments loaded from database.")
    except Error as e:
        print(f"Error loading appointments from database: {e}")
    finally:
        if conn:
            cur.close()
            conn.close()
    return appointments

def test_db_connection():
    """Тестирует подключение к базе данных"""
    try:
        print("Testing database connection...")
        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()
        cur.execute("SELECT 1;")
        result = cur.fetchone()
        cur.close()
        conn.close()
        print("✅ Database connection successful!")
        return True
    except Error as e:
        print(f"❌ Database connection failed: {e}")
        return False

# Test database connection first
if not test_db_connection():
    print("❌ Cannot start bot - database connection failed!")
    exit(1)

# Ensure table exists and load appointments
create_appointments_table()
appointments = load_appointments_from_db()

print(f"📊 Loaded {len(appointments)} appointments from database")
print(f"👤 Admin ID: {ADMIN_ID}")
print(f"🔗 Database: {DB_NAME} on {DB_HOST}:{DB_PORT}")

def add_appointment_to_db(service_name, appt_date, appt_time):
    conn = None
    try:
        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()
        cur.execute(
            """
            INSERT INTO appointments (service_name, appointment_date, appointment_time)
            VALUES (%s, %s, %s) RETURNING id;
            """,
            (service_name, appt_date, appt_time)
        )
        appointment_id = cur.fetchone()[0]
        conn.commit()
        print(f"Appointment added: {service_name} on {appt_date} at {appt_time}")
        return appointment_id
    except errors.UniqueViolation:
        print("Error: This appointment already exists.")
        return None
    except Error as e:
        print(f"Error adding appointment: {e}")
        return None
    finally:
        if conn:
            cur.close()
            conn.close()

def book_appointment_in_db(appointment_id, user_id, user_name):
    conn = None
    try:
        print(f"DEBUG: Attempting to book appointment {appointment_id} for user {user_id} ({user_name})")
        print(f"DEBUG: DB connection params - Host: {DB_HOST}, DB: {DB_NAME}, User: {DB_USER}, Port: {DB_PORT}")

        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()

        # First, check if appointment exists and is available
        cur.execute(
            "SELECT id, service_name, appointment_date, appointment_time, is_booked FROM appointments WHERE id = %s",
            (appointment_id,)
        )
        existing_appt = cur.fetchone()

        if not existing_appt:
            print(f"DEBUG: Appointment {appointment_id} not found in database")
            return None

        if existing_appt[4]:  # is_booked is True
            print(f"DEBUG: Appointment {appointment_id} is already booked")
            return None

        print(f"DEBUG: Found available appointment: {existing_appt}")

        # Now update the appointment
        cur.execute(
            """
            UPDATE appointments
            SET is_booked = TRUE, booked_by_user_id = %s, booked_by_user_name = %s
            WHERE id = %s AND is_booked = FALSE RETURNING service_name, appointment_date, appointment_time;
            """,
            (user_id, user_name, appointment_id)
        )
        booked_appt = cur.fetchone()

        if booked_appt:
            conn.commit()
            print(f"SUCCESS: Appointment {appointment_id} booked by {user_name} (ID: {user_id})")
            print(f"DEBUG: Booked appointment details: {booked_appt}")
            return booked_appt
        else:
            print(f"WARNING: UPDATE query returned no rows for appointment {appointment_id}")
            conn.rollback()
            return None

    except Error as e:
        print(f"ERROR: Database error while booking appointment {appointment_id}: {e}")
        print(f"ERROR: Error type: {type(e).__name__}")
        if conn:
            conn.rollback()
        return None
    except Exception as e:
        print(f"ERROR: Unexpected error while booking appointment {appointment_id}: {e}")
        print(f"ERROR: Error type: {type(e).__name__}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            cur.close()
            conn.close()
            print(f"DEBUG: Database connection closed for appointment {appointment_id}")

print("Starting polling bot...")

marker = 0 # Use 0 for the first page to get all uncommitted updates

while True:
    try:
        print(f"Polling for updates with marker: {marker}")
        # Get updates using long polling
        api_response = subscriptions_api.get_updates(timeout=30, marker=marker, types=Update.update_types)
        
        if api_response.updates:
            print(f"Received {len(api_response.updates)} updates.")
            for update in api_response.updates:
                print(f"DEBUG: Processing update of type: {type(update)}")
                if isinstance(update, MessageCreatedUpdate):
                    message = update.message
                    if message and message.sender:
                        user_id = message.sender.user_id
                        user_name = message.sender.name or message.sender.first_name or f"User {user_id}"
                        message_text = message.body.text if hasattr(message.body, 'text') and message.body.text else ""

                        print(f"Message from {user_name} (ID: {user_id}): {message_text}")

                        # Admin commands
                        if str(user_id) == ADMIN_ID:
                            if message_text.startswith("/add_appointment"):
                                parts = message_text.split(" ", 3) # /add_appointment <service> <date> <time>
                                if len(parts) == 4:
                                    service_name = parts[1]
                                    appt_date = parts[2]
                                    appt_time = parts[3]
                                    appt_id = add_appointment_to_db(service_name, appt_date, appt_time)
                                    if appt_id:
                                        messages_api.send_message(max_client.NewMessageBody(text=f"Запись '{service_name}' на {appt_date} в {appt_time} добавлена (ID: {appt_id})."), user_id=user_id)
                                    else:
                                        messages_api.send_message(max_client.NewMessageBody(text="Ошибка при добавлении записи. Возможно, такая запись уже существует."), user_id=user_id)
                                else:
                                    messages_api.send_message(max_client.NewMessageBody(text="Использование: /add_appointment <услуга> <дата YYYY-MM-DD> <время HH:MM:SS>"), user_id=user_id)
                            elif message_text == "/list_appointments":
                                all_appointments = load_appointments_from_db()
                                if all_appointments:
                                    response_text = "\n\nВсе записи:\n"
                                    for appt in all_appointments:
                                        status = "(Забронировано)" if appt['is_booked'] else "(Свободно)"
                                        response_text += f"- {appt['service_name']} {appt['date']} в {appt['time']} {status}\n"
                                else:
                                    response_text = "\n\nНет доступных записей."
                                messages_api.send_message(max_client.NewMessageBody(text=response_text), user_id=user_id)
                            else:
                                # Default response for admin if not a command
                                messages_api.send_message(max_client.NewMessageBody(text=f"Привет, {user_name}! Я бот для записи. Используйте /add_appointment или /list_appointments."), user_id=user_id)
                        else: # User messages
                            appointments = load_appointments_from_db() # Load appointments here for each user request
                            available_appointments = [appt for appt in appointments if not appt['is_booked']]

                            appointments_text = ""
                            buttons = []
                            if available_appointments:
                                appointments_text = "\n\nДоступные услуги и время для записи:\n"
                                for appt in available_appointments:
                                    button_text = f"{appt['service_name']} {appt['date']} в {appt['time']}"
                                    # Payload will be the appointment ID for booking
                                    buttons.append(max_client.CallbackButton(text=button_text, payload=json.dumps({'action': 'select_appointment', 'id': appt['id']})))
                            else:
                                appointments_text = "\n\nК сожалению, пока нет доступных занятий."

                            message_attachments = []
                            if buttons:
                                keyboard_rows = [[button] for button in buttons]
                                inline_keyboard_payload = max_client.InlineKeyboardAttachmentRequestPayload(buttons=keyboard_rows)
                                inline_keyboard_attachment = max_client.InlineKeyboardAttachmentRequest(payload=inline_keyboard_payload)
                                message_attachments.append(inline_keyboard_attachment)

                            new_message_body = max_client.NewMessageBody(
                                text=f"Привет, {user_name}!{appointments_text}Я могу помочь вам записаться. Выберите удобное время:",
                                attachments=message_attachments
                            )
                            try:
                                messages_api.send_message(new_message_body, user_id=user_id)
                            except ApiException as send_e:
                                print(f"Error sending message to user {user_id}: {send_e}")

                elif isinstance(update, MessageCallbackUpdate):
                    print("DEBUG: Callback query update received.")
                    callback_id = update.callback.callback_id
                    user_id = update.callback.user.user_id
                    user_name = update.callback.user.name if hasattr(update.callback.user, 'name') and update.callback.user.name else f"User {user_id}"
                    raw_payload = update.callback.payload
                    print(f"DEBUG: Raw payload: {raw_payload}")
                    print(f"DEBUG: Callback ID: {callback_id}, User ID: {user_id}, User Name: {user_name}")
                    
                    try:
                        payload_data = json.loads(raw_payload)
                        action = payload_data.get('action')
                        appointment_id = payload_data.get('id')

                        print(f"Callback query from {user_name} (ID: {user_id}) - Action: {action}, Appt ID: {appointment_id}")

                        if action == 'select_appointment':
                            # User selected an appointment, ask for confirmation
                            confirm_payload = json.dumps({'action': 'confirm_booking', 'id': appointment_id})
                            cancel_payload = json.dumps({'action': 'cancel_booking', 'id': appointment_id})

                            confirm_button = max_client.CallbackButton(text="Да, подтверждаю", payload=confirm_payload)
                            cancel_button = max_client.CallbackButton(text="Нет, отменить", payload=cancel_payload)

                            keyboard_rows = [[confirm_button], [cancel_button]]
                            inline_keyboard_payload = max_client.InlineKeyboardAttachmentRequestPayload(buttons=keyboard_rows)
                            inline_keyboard_attachment = max_client.InlineKeyboardAttachmentRequest(payload=inline_keyboard_payload)

                            messages_api.send_message(
                                max_client.NewMessageBody(
                                    text="Подтверждаете запись?",
                                    attachments=[inline_keyboard_attachment]
                                ),
                                user_id=user_id
                            )
                            messages_api.answer_on_callback(callback_id, max_client.CallbackAnswer()) # Acknowledge the callback
                        elif action == 'confirm_booking':
                            # User confirmed booking
                            print(f"DEBUG: User {user_id} ({user_name}) confirming booking for appointment {appointment_id}")
                            booked_appt_info = book_appointment_in_db(int(appointment_id), str(user_id), user_name)
                            if booked_appt_info:
                                service, date, time = booked_appt_info
                                success_message = f"✅ Спасибо за запись!\n\nУслуга: {service}\nДата: {date}\nВремя: {time}\n\nМы свяжемся с вами!"
                                messages_api.send_message(max_client.NewMessageBody(text=success_message), user_id=user_id)
                                messages_api.answer_on_callback(callback_id, max_client.CallbackAnswer()) # Acknowledge the callback

                                # Notify admin
                                if ADMIN_ID:
                                    admin_notification_text = f"🎉 Новая запись!\n\nУслуга: {service}\nДата: {date}\nВремя: {time}\nЗаписался: {user_name} (ID: {user_id})"
                                    try:
                                        messages_api.send_message(max_client.NewMessageBody(text=admin_notification_text), user_id=ADMIN_ID)
                                        print(f"DEBUG: Admin notification sent successfully")
                                    except Exception as admin_e:
                                        print(f"ERROR: Failed to send admin notification: {admin_e}")
                                print(f"SUCCESS: Booking completed for user {user_id}")
                            else:
                                error_message = "❌ Не удалось записаться.\n\nВозможные причины:\n• Запись уже занята\n• Техническая ошибка\n\nПопробуйте выбрать другое время или обратитесь к администратору."
                                messages_api.send_message(max_client.NewMessageBody(text=error_message), user_id=user_id)
                                messages_api.answer_on_callback(callback_id, max_client.CallbackAnswer()) # Acknowledge the callback
                                print(f"ERROR: Booking failed for user {user_id}, appointment {appointment_id}")
                        elif action == 'cancel_booking':
                            # User cancelled booking
                            messages_api.send_message(max_client.NewMessageBody(text="Запись отменена."), user_id=user_id)
                            messages_api.answer_on_callback(callback_id, max_client.CallbackAnswer()) # Acknowledge the callback

                    except json.JSONDecodeError:
                        print(f"Error decoding JSON payload: {callback_query.payload}")
                        messages_api.send_message(max_client.NewMessageBody(text="Произошла ошибка. Пожалуйста, попробуйте еще раз."), user_id=user_id)
                    except Exception as e:
                        print(f"An unexpected error occurred during callback processing: {e}")
                        messages_api.send_message(max_client.NewMessageBody(text="Произошла ошибка. Пожалуйста, попробуйте еще раз."), user_id=user_id)

                

            # Update marker for the next poll
            marker = api_response.marker
            print(f"Updated marker to: {marker}")

    except ApiException as e:
        print(f"Exception when calling SubscriptionsApi->get_updates: {e}")
        time.sleep(5) # Wait before retrying on API error
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        time.sleep(5) # Wait before retrying on other errors
