#!/usr/bin/env python3
"""
Скрипт для обновления webhook URL в Max API
"""

import os
import max_client
from dotenv import load_dotenv

load_dotenv()

# Конфигурация
MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
WEBHOOK_URL = os.getenv('WEBHOOK_URL')

def update_webhook():
    """Обновить webhook URL"""
    try:
        # Настройка клиента
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN

        # API для работы с подписками
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)

        print("🔧 Обновление webhook...")
        print(f"📡 URL: {WEBHOOK_URL}")

        # Сначала получаем текущие подписки и удаляем их
        try:
            current_subscriptions = subscriptions_api.get_subscriptions()
            if hasattr(current_subscriptions, 'subscriptions') and current_subscriptions.subscriptions:
                for subscription in current_subscriptions.subscriptions:
                    try:
                        subscriptions_api.unsubscribe(subscription.url)
                        print(f"🗑️ Удалена старая подписка: {subscription.url}")
                    except Exception as e:
                        print(f"⚠️ Ошибка удаления подписки: {e}")
        except Exception as e:
            print(f"📋 Не удалось получить подписки: {e}")

        # Создаем новую подписку
        subscription_body = max_client.SubscriptionRequestBody(
            url=WEBHOOK_URL,
            update_types=['message_created', 'message_callback'],
            version='0.1.0'
        )

        response = subscriptions_api.subscribe(subscription_body)

        print(f"✅ Webhook успешно обновлен!")
        print(f"🔗 URL: {WEBHOOK_URL}")
        print(f"📋 Ответ: {response}")

    except Exception as e:
        print(f"❌ Ошибка обновления webhook: {e}")

if __name__ == "__main__":
    update_webhook()
