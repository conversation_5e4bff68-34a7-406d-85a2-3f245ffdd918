#!/usr/bin/env python3
"""
Скрипт для быстрой смены webhook URL
"""

import os
import sys
from dotenv import load_dotenv, set_key, find_dotenv

def set_webhook_url(new_url):
    """Установить новый webhook URL"""
    
    # Находим .env файл
    env_file = find_dotenv()
    if not env_file:
        env_file = '.env'
    
    # Добавляем /webhook/max если не указан
    if not new_url.endswith('/webhook/max'):
        if new_url.endswith('/'):
            new_url = new_url + 'webhook/max'
        else:
            new_url = new_url + '/webhook/max'
    
    # Устанавливаем в .env файл
    set_key(env_file, 'WEBHOOK_URL', new_url)
    
    print(f"✅ Webhook URL установлен: {new_url}")
    print(f"📝 Сохранено в файл: {env_file}")
    
    return new_url

def get_current_url():
    """Получить текущий webhook URL"""
    load_dotenv()
    current_url = os.getenv('WEBHOOK_URL', 'https://closing-pup-flying.ngrok-free.app/webhook/max')
    return current_url

def setup_webhook_with_new_url(webhook_url):
    """Настроить webhook с новым URL"""
    import max_client
    from max_client.rest import ApiException
    
    try:
        # Загружаем переменные окружения
        load_dotenv()
        
        MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
        if not MAX_BOT_TOKEN:
            print("❌ Не найден MAX_BOT_TOKEN в .env файле")
            return False
        
        # Инициализация API клиента
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN
        
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)
        
        print(f"🔧 Настройка webhook с новым URL...")
        print(f"📡 URL: {webhook_url}")
        
        # Получаем текущие подписки и удаляем их
        try:
            current_subscriptions = subscriptions_api.get_subscriptions()
            if hasattr(current_subscriptions, 'subscriptions') and current_subscriptions.subscriptions:
                for subscription in current_subscriptions.subscriptions:
                    try:
                        subscriptions_api.unsubscribe(subscription.url)
                        print(f"🗑️ Удалена старая подписка: {subscription.url}")
                    except ApiException as e:
                        print(f"⚠️ Ошибка удаления подписки: {e}")
        except ApiException as e:
            print(f"📋 Не удалось получить подписки: {e}")
        
        # Создаем новую подписку
        subscription_body = max_client.SubscriptionRequestBody(
            url=webhook_url,
            update_types=['message_created', 'message_callback'],
            version='0.1.0'
        )
        
        # Устанавливаем webhook
        result = subscriptions_api.subscribe(subscription_body)
        
        print("✅ Webhook успешно настроен!")
        print(f"📊 Результат: {result}")
        
        # Проверяем установку
        subscriptions = subscriptions_api.get_subscriptions()
        print(f"🔍 Проверка подписок: {subscriptions}")
        
        return True
        
    except ApiException as e:
        print(f"❌ Ошибка API: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def main():
    """Главная функция"""
    
    if len(sys.argv) < 2:
        current_url = get_current_url()
        print("🔗 Управление Webhook URL")
        print("=" * 50)
        print(f"📍 Текущий URL: {current_url}")
        print("")
        print("📖 Использование:")
        print("  python set_webhook_url.py <новый_домен>")
        print("  python set_webhook_url.py show")
        print("")
        print("🌐 Примеры:")
        print("  python set_webhook_url.py https://abc-123.ngrok-free.app")
        print("  python set_webhook_url.py https://mybot.example.com")
        print("  python set_webhook_url.py show")
        print("")
        print("💡 Путь /webhook/max добавится автоматически")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command.lower() == 'show':
        current_url = get_current_url()
        print(f"📍 Текущий webhook URL: {current_url}")
        return
    
    # Устанавливаем новый URL
    new_url = command
    
    # Проверяем формат URL
    if not new_url.startswith('http'):
        print("❌ URL должен начинаться с http:// или https://")
        sys.exit(1)
    
    try:
        # Устанавливаем URL в .env
        final_url = set_webhook_url(new_url)
        
        # Спрашиваем, нужно ли сразу настроить webhook
        response = input("\n🤖 Настроить webhook в Max API сейчас? (y/n): ").lower()
        
        if response in ['y', 'yes', 'да', 'д']:
            print("\n🚀 Настройка webhook...")
            success = setup_webhook_with_new_url(final_url)
            
            if success:
                print("\n🎉 Готово! Webhook настроен с новым URL")
                print(f"📡 Активный URL: {final_url}")
                print("\n💡 Теперь можно тестировать бота!")
            else:
                print("\n💥 Ошибка настройки webhook")
                print("🔧 Попробуйте настроить вручную:")
                print(f"   python setup_webhook.py setup")
        else:
            print("\n📝 URL сохранен в .env файле")
            print("🔧 Для настройки webhook выполните:")
            print("   python setup_webhook.py setup")
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
