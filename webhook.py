#!/usr/bin/env python3
"""
Простой скрипт для настройки webhook
"""

import os
import max_client
from dotenv import load_dotenv

load_dotenv()

MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')

def setup_webhook(webhook_url):
    """Настроить webhook"""
    try:
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN
        
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)
        
        print(f"🔧 Настройка webhook: {webhook_url}")
        
        # Удаляем старые подписки
        try:
            current = subscriptions_api.get_subscriptions()
            if hasattr(current, 'subscriptions') and current.subscriptions:
                for sub in current.subscriptions:
                    subscriptions_api.unsubscribe(sub.url)
                    print(f"🗑️ Удалена: {sub.url}")
        except Exception as e:
            print(f"⚠️ Ошибка получения подписок: {e}")
        
        # Создаем новую подписку
        subscription_body = max_client.SubscriptionRequestBody(
            url=webhook_url,
            update_types=['message_created', 'message_callback'],
            version='0.1.0'
        )
        
        response = subscriptions_api.subscribe(subscription_body)
        print(f"✅ Webhook настроен!")
        print(f"📋 Ответ: {response}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("Использование: python webhook.py https://your-ngrok-url.ngrok-free.app/webhook/max")
        sys.exit(1)
    
    webhook_url = sys.argv[1]
    setup_webhook(webhook_url)
