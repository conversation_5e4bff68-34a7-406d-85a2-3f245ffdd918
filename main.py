#!/usr/bin/env python3
"""
FastAPI Max Bot с Webhook для записи на приемы
Максимальная производительность с асинхронной обработкой
"""

import os
import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import asyncpg
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv
import max_client
from max_client.rest import ApiException

# Загрузка переменных окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Конфигурация
MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_NAME = os.getenv('DB_NAME')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = int(os.getenv('DB_PORT', 5432))
ADMIN_ID = int(os.getenv('ADMIN_ID'))

# Webhook URL (будет установлен через ngrok)
WEBHOOK_URL = "https://closing-pup-flying.ngrok-free.app/webhook/max"

# FastAPI приложение
app = FastAPI(title="Max Bot Webhook", version="1.0.0")

# Глобальные переменные
db_pool: Optional[asyncpg.Pool] = None
max_api_client = None
messages_api = None

# Pydantic модели для webhook
class WebhookUpdate(BaseModel):
    update_id: int
    timestamp: int
    update_type: str
    payload: Dict[str, Any]

class CallbackData(BaseModel):
    callback_id: str
    user_id: int
    user_name: str
    payload: Optional[str] = None

class MessageData(BaseModel):
    message_id: str
    user_id: int
    user_name: str
    text: Optional[str] = None
    chat_id: Optional[int] = None

# Инициализация Max API клиента
def init_max_client():
    """Инициализация Max API клиента"""
    global max_api_client, messages_api
    
    configuration = max_client.Configuration()
    configuration.api_key['access_token'] = MAX_BOT_TOKEN
    
    max_api_client = max_client.ApiClient(configuration)
    messages_api = max_client.MessagesApi(max_api_client)
    
    logger.info("Max API клиент инициализирован")

# Инициализация базы данных
async def init_database():
    """Создание пула соединений с базой данных"""
    global db_pool
    
    try:
        db_pool = await asyncpg.create_pool(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            min_size=5,
            max_size=20,
            command_timeout=60
        )
        logger.info("Пул соединений с БД создан")
        
        # Проверяем структуру таблицы
        async with db_pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS appointments (
                    id SERIAL PRIMARY KEY,
                    service_name VARCHAR(255) NOT NULL,
                    appointment_date DATE NOT NULL,
                    appointment_time TIME NOT NULL,
                    is_booked BOOLEAN DEFAULT FALSE,
                    booked_by_user_id BIGINT,
                    booked_by_user_name VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("Таблица appointments проверена/создана")
            
    except Exception as e:
        logger.error(f"Ошибка инициализации БД: {e}")
        raise

# Асинхронные функции для работы с БД
async def get_available_appointments():
    """Получить доступные записи"""
    async with db_pool.acquire() as conn:
        rows = await conn.fetch("""
            SELECT id, service_name, appointment_date, appointment_time 
            FROM appointments 
            WHERE is_booked = FALSE 
            ORDER BY appointment_date, appointment_time
            LIMIT 10
        """)
        return [dict(row) for row in rows]

async def book_appointment_async(appointment_id: int, user_id: int, user_name: str) -> bool:
    """Асинхронное бронирование записи"""
    async with db_pool.acquire() as conn:
        result = await conn.execute("""
            UPDATE appointments 
            SET is_booked = TRUE, booked_by_user_id = $1, booked_by_user_name = $2
            WHERE id = $3 AND is_booked = FALSE
        """, user_id, user_name, appointment_id)
        
        # Проверяем, была ли обновлена запись
        return result.split()[-1] == '1'

# Асинхронные функции для Max API
async def send_message_async(text: str, user_id: int) -> bool:
    """Асинхронная отправка сообщения через Max API"""
    try:
        loop = asyncio.get_event_loop()
        
        # Создаем тело сообщения
        message_body = max_client.NewMessageBody()
        message_body.text = text
        
        # Отправляем в отдельном потоке
        result = await loop.run_in_executor(
            None,
            lambda: messages_api.send_message(message_body, user_id=user_id)
        )
        
        logger.info(f"Сообщение отправлено пользователю {user_id}")
        return True
        
    except ApiException as e:
        logger.error(f"Ошибка отправки сообщения: {e}")
        return False
    except Exception as e:
        logger.error(f"Неожиданная ошибка при отправке: {e}")
        return False

async def send_keyboard_async(text: str, user_id: int, appointments: list) -> bool:
    """Отправка клавиатуры с доступными записями"""
    try:
        loop = asyncio.get_event_loop()
        
        # Создаем кнопки для записей
        buttons = []
        for apt in appointments:
            date_str = apt['appointment_date'].strftime('%d.%m')
            time_str = apt['appointment_time'].strftime('%H:%M')
            button_text = f"{apt['service_name']} - {date_str} {time_str}"
            
            button = max_client.Button()
            button.text = button_text
            button.payload = f"book_{apt['id']}"
            buttons.append([button])
        
        # Создаем клавиатуру
        keyboard_payload = max_client.InlineKeyboardAttachmentRequestPayload()
        keyboard_payload.buttons = buttons
        
        keyboard_attachment = max_client.InlineKeyboardAttachmentRequest()
        keyboard_attachment.payload = keyboard_payload
        
        # Создаем сообщение
        message_body = max_client.NewMessageBody()
        message_body.text = text
        message_body.attachments = [keyboard_attachment]
        
        # Отправляем
        result = await loop.run_in_executor(
            None,
            lambda: messages_api.send_message(message_body, user_id=user_id)
        )
        
        logger.info(f"Клавиатура отправлена пользователю {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Ошибка отправки клавиатуры: {e}")
        return False

# Обработчики событий
async def handle_message_async(message_data: MessageData):
    """Асинхронная обработка текстового сообщения"""
    try:
        text = message_data.text.lower() if message_data.text else ""
        user_id = message_data.user_id
        
        if text in ['/start', 'начать', 'записаться']:
            # Получаем доступные записи
            appointments = await get_available_appointments()
            
            if appointments:
                await send_keyboard_async(
                    "🗓 Выберите удобное время для записи:",
                    user_id,
                    appointments
                )
            else:
                await send_message_async(
                    "😔 К сожалению, свободных записей нет. Попробуйте позже.",
                    user_id
                )
        else:
            await send_message_async(
                "👋 Привет! Напишите /start для записи на прием.",
                user_id
            )
            
    except Exception as e:
        logger.error(f"Ошибка обработки сообщения: {e}")

async def handle_callback_async(callback_data: CallbackData):
    """Асинхронная обработка callback от кнопок"""
    try:
        payload = callback_data.payload
        user_id = callback_data.user_id
        user_name = callback_data.user_name
        
        if payload and payload.startswith('book_'):
            appointment_id = int(payload.split('_')[1])
            
            # Пытаемся забронировать
            success = await book_appointment_async(appointment_id, user_id, user_name)
            
            if success:
                await send_message_async(
                    f"✅ Отлично! Вы записаны на прием.\n"
                    f"Ваше имя: {user_name}\n"
                    f"ID записи: {appointment_id}",
                    user_id
                )
            else:
                await send_message_async(
                    "😔 Извините, это время уже занято. Выберите другое время.",
                    user_id
                )
        
    except Exception as e:
        logger.error(f"Ошибка обработки callback: {e}")

# FastAPI endpoints
@app.on_event("startup")
async def startup_event():
    """Инициализация при запуске"""
    logger.info("Запуск FastAPI приложения...")
    init_max_client()
    await init_database()
    logger.info("Приложение готово к работе!")

@app.on_event("shutdown")
async def shutdown_event():
    """Очистка при завершении"""
    if db_pool:
        await db_pool.close()
    logger.info("Приложение завершено")

@app.get("/")
async def root():
    """Главная страница"""
    return {"message": "Max Bot Webhook Server", "status": "running"}

@app.post("/webhook/max")
async def webhook_handler(request: Request, background_tasks: BackgroundTasks):
    """Основной обработчик webhook от Max"""
    try:
        # Получаем данные
        data = await request.json()
        logger.info(f"Получен webhook: {data}")
        
        # Добавляем обработку в фоновые задачи
        background_tasks.add_task(process_webhook_async, data)
        
        # Быстрый ответ
        return {"status": "ok"}
        
    except Exception as e:
        logger.error(f"Ошибка webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_webhook_async(data: Dict[str, Any]):
    """Асинхронная обработка webhook данных"""
    try:
        update_type = data.get('update_type')
        payload = data.get('payload', {})
        
        if update_type == 'message_created':
            # Обработка нового сообщения
            message_data = MessageData(
                message_id=payload.get('message', {}).get('message_id', ''),
                user_id=payload.get('message', {}).get('sender', {}).get('user_id', 0),
                user_name=payload.get('message', {}).get('sender', {}).get('name', ''),
                text=payload.get('message', {}).get('body', {}).get('text', ''),
                chat_id=payload.get('message', {}).get('recipient', {}).get('chat_id')
            )
            await handle_message_async(message_data)
            
        elif update_type == 'message_callback':
            # Обработка callback от кнопок
            callback_data = CallbackData(
                callback_id=payload.get('callback', {}).get('callback_id', ''),
                user_id=payload.get('callback', {}).get('user', {}).get('user_id', 0),
                user_name=payload.get('callback', {}).get('user', {}).get('name', ''),
                payload=payload.get('callback', {}).get('payload', '')
            )
            await handle_callback_async(callback_data)
            
    except Exception as e:
        logger.error(f"Ошибка обработки webhook: {e}")

@app.get("/health")
async def health_check():
    """Проверка здоровья сервиса"""
    try:
        # Проверяем БД
        async with db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
