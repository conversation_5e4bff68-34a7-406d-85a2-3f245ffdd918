#!/usr/bin/env python3
"""
FastAPI Max Bot - модульная версия на основе старого polling_bot_pg.py
"""

import uvicorn
import logging
from datetime import datetime
from fastapi import FastAPI, BackgroundTasks, Request
from database import init_db, close_db, clear_appointments
from bot_handlers import handle_message, handle_callback

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI приложение
app = FastAPI(title="Max Bot Webhook")

@app.on_event("startup")
async def startup():
    """Инициализация при запуске"""
    logger.info("🚀 Запуск FastAPI приложения...")
    
    # Инициализируем БД
    await init_db()
    
    # Очищаем старые записи
    cleared_count = await clear_appointments()
    logger.info(f"🗑️ Очищено {cleared_count} старых записей")
    
    logger.info("✅ Приложение готово к работе!")

@app.on_event("shutdown")
async def shutdown():
    """Очистка при завершении"""
    await close_db()
    logger.info("🛑 Приложение завершено")

@app.post("/webhook/max")
async def webhook_handler(request: Request, background_tasks: BackgroundTasks):
    """Обработчик webhook от Max API"""
    try:
        payload = await request.json()
        logger.info(f"📨 Получен webhook: {payload}")
        
        update_type = payload.get('update_type')
        
        if update_type == 'message_created':
            # Текстовое сообщение
            user_id = payload.get('sender', {}).get('user_id')
            user_name = payload.get('sender', {}).get('name', f'User {user_id}')
            message_text = payload.get('text', '')
            
            if user_id and message_text:
                background_tasks.add_task(handle_message, user_id, user_name, message_text)
        
        elif update_type == 'message_callback':
            # Callback от кнопки
            callback_data = payload.get('callback', {})
            user_id = callback_data.get('user', {}).get('user_id')
            user_name = callback_data.get('user', {}).get('name', f'User {user_id}')
            callback_payload = callback_data.get('payload', '')
            callback_id = callback_data.get('callback_id', '')
            
            if user_id and callback_payload:
                background_tasks.add_task(handle_callback, user_id, user_name, callback_payload, callback_id)
        
        return {"status": "ok"}
        
    except Exception as e:
        logger.error(f"❌ Ошибка обработки webhook: {e}")
        return {"status": "error", "message": str(e)}

@app.get("/health")
async def health_check():
    """Проверка здоровья сервиса"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/")
async def root():
    """Корневой эндпоинт"""
    return {"message": "Max Bot Webhook Server", "status": "running"}

if __name__ == "__main__":
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    uvicorn.run(app, host="0.0.0.0", port=port, reload=True)
