#!/usr/bin/env python3
"""
Простой скрипт для тестирования авторизации и подключения к PostgreSQL
"""

import psycopg2
import sys
import time
import os
from datetime import datetime
from pathlib import Path

def load_env_file(env_path='.env'):
    """Загружает переменные окружения из .env файла"""
    env_file = Path(env_path)
    if env_file.exists():
        print(f"📁 Загружаем переменные из {env_path}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Удаляем кавычки если они есть
                    value = value.strip('"\'')
                    os.environ[key.strip()] = value
    else:
        print(f"⚠️  Файл {env_path} не найден, используем переменные окружения")

def get_db_config():
    """Получает конфигурацию базы данных из переменных окружения"""
    # Загружаем .env файл если он существует
    load_env_file()

    # Конфигурация подключения из переменных окружения
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'database': os.getenv('DB_NAME', 'stress_test_db'),
        'user': os.getenv('DB_USER', 'test_user'),
        'password': os.getenv('DB_PASSWORD', 'test_password'),
        'port': int(os.getenv('DB_PORT', '5433'))
    }

    print("🔧 Конфигурация базы данных:")
    for key, value in config.items():
        if key == 'password':
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")

    return config

def test_connection():
    """Тестирует подключение к базе данных"""
    try:
        db_config = get_db_config()
        print(f"\n[{datetime.now()}] Попытка подключения к PostgreSQL...")

        # Подключение к базе данных
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print("✅ Подключение успешно!")

        # Проверка версии PostgreSQL
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"📊 Версия PostgreSQL: {version}")

        # Проверка текущего времени на сервере
        cursor.execute("SELECT NOW();")
        server_time = cursor.fetchone()[0]
        print(f"🕐 Время на сервере: {server_time}")

        # Проверка количества активных подключений
        cursor.execute("SELECT count(*) FROM pg_stat_activity;")
        active_connections = cursor.fetchone()[0]
        print(f"🔗 Активных подключений: {active_connections}")

        cursor.close()
        conn.close()

        print("✅ Тест авторизации прошел успешно!")
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка подключения к PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def create_test_table():
    """Создает тестовую таблицу для стресс-тестов"""
    try:
        db_config = get_db_config()
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print(f"[{datetime.now()}] Создание тестовой таблицы...")

        # Создание таблицы пользователей
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            );
        """)

        # Создание индексов для оптимизации
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);")

        conn.commit()
        print("✅ Тестовая таблица 'users' создана успешно!")

        # Проверка структуры таблицы
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'users'
            ORDER BY ordinal_position;
        """)

        columns = cursor.fetchall()
        print("📋 Структура таблицы 'users':")
        for col in columns:
            print(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")

        cursor.close()
        conn.close()
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка создания таблицы: {e}")
        return False

def insert_test_data(count=100):
    """Вставляет тестовые данные"""
    try:
        db_config = get_db_config()
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print(f"[{datetime.now()}] Вставка {count} тестовых записей...")

        for i in range(count):
            cursor.execute("""
                INSERT INTO users (username, email, password_hash)
                VALUES (%s, %s, %s)
                ON CONFLICT (username) DO NOTHING;
            """, (f"user_{i}", f"user_{i}@test.com", f"hash_{i}"))

        conn.commit()

        # Проверка количества записей
        cursor.execute("SELECT COUNT(*) FROM users;")
        total_users = cursor.fetchone()[0]
        print(f"✅ Всего пользователей в таблице: {total_users}")

        cursor.close()
        conn.close()
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка вставки данных: {e}")
        return False

def main():
    """Основная функция"""
    print("=" * 60)
    print("🚀 ТЕСТ АВТОРИЗАЦИИ И ПОДКЛЮЧЕНИЯ К POSTGRESQL")
    print("=" * 60)

    # Тест подключения
    if not test_connection():
        sys.exit(1)

    print("\n" + "-" * 60)

    # Создание тестовой таблицы
    if not create_test_table():
        sys.exit(1)

    print("\n" + "-" * 60)

    # Вставка тестовых данных
    if not insert_test_data(50):
        sys.exit(1)

    print("\n" + "=" * 60)
    print("✅ ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
    print("🎯 База данных готова для стресс-тестирования")
    print("=" * 60)

if __name__ == "__main__":
    main()
