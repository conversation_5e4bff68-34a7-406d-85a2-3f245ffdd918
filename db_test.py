#!/usr/bin/env python3
"""
Простой скрипт для тестирования авторизации и подключения к PostgreSQL
"""

import psycopg2
import sys
import time
import os
from datetime import datetime
from pathlib import Path

def load_env_file(env_path='.env'):
    """Загружает переменные окружения из .env файла"""
    env_file = Path(env_path)
    if env_file.exists():
        print(f"📁 Загружаем переменные из {env_path}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Удаляем кавычки если они есть
                    value = value.strip('"\'')
                    os.environ[key.strip()] = value
    else:
        print(f"⚠️  Файл {env_path} не найден, используем переменные окружения")

def get_db_config():
    """Получает конфигурацию базы данных из переменных окружения"""
    # Загружаем .env файл если он существует
    load_env_file()

    # Конфигурация подключения из переменных окружения
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'database': os.getenv('DB_NAME', 'stress_test_db'),
        'user': os.getenv('DB_USER', 'test_user'),
        'password': os.getenv('DB_PASSWORD', 'test_password'),
        'port': int(os.getenv('DB_PORT', '5433'))
    }

    print("🔧 Конфигурация базы данных:")
    for key, value in config.items():
        if key == 'password':
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")

    return config

def test_connection():
    """Тестирует подключение к базе данных"""
    try:
        db_config = get_db_config()
        print(f"\n[{datetime.now()}] Попытка подключения к PostgreSQL...")

        # Подключение к базе данных
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print("✅ Подключение успешно!")

        # Проверка версии PostgreSQL
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"📊 Версия PostgreSQL: {version}")

        # Проверка текущего времени на сервере
        cursor.execute("SELECT NOW();")
        server_time = cursor.fetchone()[0]
        print(f"🕐 Время на сервере: {server_time}")

        # Проверка количества активных подключений
        cursor.execute("SELECT count(*) FROM pg_stat_activity;")
        active_connections = cursor.fetchone()[0]
        print(f"🔗 Активных подключений: {active_connections}")

        cursor.close()
        conn.close()

        print("✅ Тест авторизации прошел успешно!")
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка подключения к PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def show_database_info(db_config):
    """Показывает информацию о базе данных"""
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print(f"\n[{datetime.now()}] Получение информации о базе данных...")

        # Информация о базе данных
        cursor.execute("SELECT current_database(), current_user, inet_server_addr(), inet_server_port();")
        db_info = cursor.fetchone()
        print(f"📊 База данных: {db_info[0]}")
        print(f"👤 Текущий пользователь: {db_info[1]}")
        print(f"🌐 Адрес сервера: {db_info[2] or 'localhost'}")
        print(f"🔌 Порт сервера: {db_info[3] or 'N/A'}")

        # Размер базы данных
        cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()));")
        db_size = cursor.fetchone()[0]
        print(f"💾 Размер базы данных: {db_size}")

        # Список всех таблиц
        cursor.execute("""
            SELECT table_name, table_type
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        print(f"\n📋 Таблицы в базе данных ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]} ({table[1]})")

        # Информация о пользователях (если таблица существует)
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'users'
            );
        """)
        users_table_exists = cursor.fetchone()[0]

        if users_table_exists:
            # Структура таблицы users
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = 'users' AND table_schema = 'public'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            print(f"\n📋 Структура таблицы 'users':")
            for col in columns:
                nullable = 'NULL' if col[2] == 'YES' else 'NOT NULL'
                default = f" DEFAULT {col[3]}" if col[3] else ""
                print(f"  - {col[0]}: {col[1]} ({nullable}){default}")

            # Количество записей в таблице users
            cursor.execute("SELECT COUNT(*) FROM users;")
            user_count = cursor.fetchone()[0]
            print(f"\n👥 Количество пользователей в таблице: {user_count}")

            # Последние 5 пользователей (если есть)
            if user_count > 0:
                cursor.execute("""
                    SELECT id, username, email, created_at, is_active
                    FROM users
                    ORDER BY created_at DESC
                    LIMIT 5;
                """)
                recent_users = cursor.fetchall()
                print(f"\n👤 Последние пользователи:")
                for user in recent_users:
                    status = "✅" if user[4] else "❌"
                    print(f"  - ID: {user[0]}, Username: {user[1]}, Email: {user[2]}, Created: {user[3]}, Active: {status}")

        cursor.close()
        conn.close()
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка получения информации о базе данных: {e}")
        return False

def clean_test_data():
    """Очищает тестовые данные"""
    try:
        db_config = get_db_config()
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        print(f"\n[{datetime.now()}] Очистка тестовых данных...")

        # Проверяем, существует ли таблица users
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'users'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if table_exists:
            # Получаем количество записей перед удалением
            cursor.execute("SELECT COUNT(*) FROM users;")
            count_before = cursor.fetchone()[0]

            if count_before > 0:
                # Удаляем все записи
                cursor.execute("DELETE FROM users;")
                conn.commit()
                print(f"✅ Удалено {count_before} записей из таблицы 'users'")
            else:
                print("ℹ️  Таблица 'users' уже пуста")
        else:
            print("ℹ️  Таблица 'users' не существует")

        cursor.close()
        conn.close()
        return True

    except psycopg2.Error as e:
        print(f"❌ Ошибка очистки данных: {e}")
        return False

def main():
    """Основная функция"""
    print("=" * 60)
    print("🔍 СТРУКТУРА БАЗЫ ДАННЫХ POSTGRESQL")
    print("=" * 60)

    # Получаем конфигурацию один раз
    db_config = get_db_config()

    # Тест подключения
    if not test_connection():
        sys.exit(1)

    print("\n" + "-" * 60)

    # Показать информацию о базе данных
    if not show_database_info(db_config):
        sys.exit(1)

    print("\n" + "=" * 60)
    print("✅ СТРУКТУРА БАЗЫ ДАННЫХ ПОКАЗАНА!")
    print("=" * 60)

if __name__ == "__main__":
    main()
