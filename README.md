# Max Bot FastAPI с Webhook

Высокопроизводительный бот для записи на приемы с использованием FastAPI и webhook.

## 🚀 Особенности

- **Асинхронная обработка** - максимальная производительность
- **Webhook архитектура** - мгновенная обработка сообщений
- **Пул соединений с БД** - эффективная работа с PostgreSQL
- **Нативная поддержка Max API** - без дополнительных библиотек
- **Масштабируемость** - готов к миллионам пользователей

## 📋 Требования

- Python 3.12+
- PostgreSQL
- Max Bot Token
- ngrok (для webhook)

## 🛠 Установка и запуск

### 1. Подготовка окружения

```bash
# Активация виртуального окружения
source venv/bin/activate

# Установка зависимостей
pip install -r requirements.txt
```

### 2. Настройка переменных окружения

Файл `.env` уже настроен:
```
MAX_BOT_TOKEN=f9LHodD0cOKxxgk3x_xe9atZy1ecE7D-leD4z7XeAaG2gBBcacWbK9-5qoAmBlVe409WhZzXhl2GzO1edjQ2
DB_HOST=localhost
DB_NAME=max_ai_bot
DB_USER=max_ai_bot
DB_PASSWORD=111
DB_PORT=5432
ADMIN_ID=2757554
```

### 3. Настройка ngrok

Убедитесь, что ngrok настроен на правильный порт:

```bash
# Для порта 8000
ngrok http 8000

# Для порта 80 (требует sudo)
ngrok http 80
```

Текущий URL: `https://closing-pup-flying.ngrok-free.app`

### 4. Добавление тестовых данных

```bash
# Добавить тестовые записи
./venv/bin/python add_test_data.py add

# Показать все записи
./venv/bin/python add_test_data.py show

# Очистить записи
./venv/bin/python add_test_data.py clear
```

### 5. Настройка webhook

```bash
# Установить webhook
./venv/bin/python setup_webhook.py setup

# Проверить webhook
./venv/bin/python setup_webhook.py check

# Удалить webhook
./venv/bin/python setup_webhook.py remove
```

### 6. Запуск сервера

```bash
# На порту 8000 (рекомендуется)
./venv/bin/python main.py 8000

# На порту 80 (требует sudo)
sudo ./venv/bin/python main.py 80

# Или использовать готовый скрипт
./run.sh
```

## 🔧 API Endpoints

- `GET /` - Главная страница
- `POST /webhook/max` - Webhook для Max API
- `GET /health` - Проверка здоровья сервиса

## 📊 Мониторинг

### Health Check
```bash
curl http://localhost:8000/health
```

Ответ:
```json
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2025-07-06T18:33:15.824891"
}
```

### Логи
Все события логируются в консоль с временными метками.

## 🎯 Использование бота

1. **Начало работы**: Отправьте `/start` или `начать`
2. **Выбор времени**: Выберите удобное время из предложенных кнопок
3. **Подтверждение**: Получите подтверждение записи

## 🏗 Архитектура

### Компоненты:
- **FastAPI** - веб-фреймворк
- **asyncpg** - асинхронный драйвер PostgreSQL
- **Max Client** - официальный клиент Max API
- **uvicorn** - ASGI сервер

### Асинхронная обработка:
- Webhook получает события мгновенно
- Обработка в фоновых задачах
- Пул соединений с БД
- Нативная async поддержка Max API

### База данных:
```sql
CREATE TABLE appointments (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(255) NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    is_booked BOOLEAN DEFAULT FALSE,
    booked_by_user_id BIGINT,
    booked_by_user_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 Миграция с polling

Старые файлы перенесены в папку `old/`:
- `old/polling_bot_pg.py` - старый polling бот
- `old/db_test.py` - тест базы данных
- `old/main.py` - старый main файл

## 🚀 Производительность

- **Webhook vs Polling**: Мгновенная обработка vs опрос каждые N секунд
- **Async операции**: Неблокирующие операции с БД и API
- **Пул соединений**: Эффективное использование ресурсов БД
- **Background tasks**: Обработка в фоне без блокировки webhook

## 🔧 Настройка для продакшена

1. **Использовать реальный домен** вместо ngrok
2. **Настроить SSL сертификат**
3. **Добавить мониторинг** (Prometheus, Grafana)
4. **Настроить логирование** в файлы
5. **Добавить кеширование** (Redis)
6. **Настроить балансировщик нагрузки**

## 📝 Логи и отладка

Все важные события логируются:
- Получение webhook
- Обработка сообщений
- Операции с БД
- Отправка сообщений
- Ошибки

## 🆘 Устранение неполадок

### Проблемы с портом 80
```bash
# Проверить, что порт свободен
sudo netstat -tlnp | grep :80

# Запустить на другом порту
./venv/bin/python main.py 8000
```

### Проблемы с webhook
```bash
# Проверить текущие подписки
./venv/bin/python setup_webhook.py check

# Переустановить webhook
./venv/bin/python setup_webhook.py remove
./venv/bin/python setup_webhook.py setup
```

### Проблемы с БД
```bash
# Проверить подключение
./venv/bin/python add_test_data.py show
```

## 📈 Следующие шаги

1. **Добавить кеширование** для улучшения производительности
2. **Реализовать мультитенантность** для разных компаний
3. **Добавить веб-интерфейс** для администрирования
4. **Настроить мониторинг** и алерты
5. **Добавить тесты** для надежности

## 🎉 Готово!

Бот готов к работе с максимальной производительностью!
