max_bot_api_client-0.2.dev0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
max_bot_api_client-0.2.dev0.dist-info/METADATA,sha256=6pV6jc4T4UHZKq9r01F--0eSlGLbLL27oXNu4ZYsmxg,1303
max_bot_api_client-0.2.dev0.dist-info/RECORD,,
max_bot_api_client-0.2.dev0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
max_bot_api_client-0.2.dev0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
max_bot_api_client-0.2.dev0.dist-info/licenses/LICENSE,sha256=46mU2C5kSwOnkqkw9XQAJlhBL2JAf1_uCD8lVcXyMRg,7652
max_bot_api_client-0.2.dev0.dist-info/top_level.txt,sha256=M1tZgdj39J5PiOZmOQGoBFIHX6weCzaVVz55mtf4Wh4,16
max_client/__init__.py,sha256=D1p6Lvf6OFy_mrl9K_91GAVN1Mk0pagYTyuttEz8dZE,17391
max_client/__pycache__/__init__.cpython-312.pyc,,
max_client/__pycache__/api_client.cpython-312.pyc,,
max_client/__pycache__/cmn.cpython-312.pyc,,
max_client/__pycache__/configuration.cpython-312.pyc,,
max_client/__pycache__/max_chat.cpython-312.pyc,,
max_client/__pycache__/max_commander.cpython-312.pyc,,
max_client/__pycache__/max_editmessage.cpython-312.pyc,,
max_client/__pycache__/max_repiter.cpython-312.pyc,,
max_client/__pycache__/rest.cpython-312.pyc,,
max_client/__pycache__/utl.cpython-312.pyc,,
max_client/api/__init__.py,sha256=1-wyOPymbquw8hXrzeyA00A-HDsS4T8cUM7GJ1DQmNg,339
max_client/api/__pycache__/__init__.cpython-312.pyc,,
max_client/api/__pycache__/bots_api.cpython-312.pyc,,
max_client/api/__pycache__/chats_api.cpython-312.pyc,,
max_client/api/__pycache__/messages_api.cpython-312.pyc,,
max_client/api/__pycache__/subscriptions_api.cpython-312.pyc,,
max_client/api/__pycache__/upload_api.cpython-312.pyc,,
max_client/api/bots_api.py,sha256=32pWioPYdILKfMbA9UMtO62bBCzWvivPIhdFQwmXdpY,16946
max_client/api/chats_api.py,sha256=QwzMaMRGzNmBIWjLhqSeYHv6Br_qwP7Fu_KyLtweDf8,72505
max_client/api/messages_api.py,sha256=7uROqnrMQegkDKaXFjloqSKB8tstAze3MJFuq97mJgI,44670
max_client/api/subscriptions_api.py,sha256=rzrhvicjB3iwr5RlVaj9JzcHCYlzOvdUZ4e1UbQ1MXw,27708
max_client/api/upload_api.py,sha256=IF-hb7tKyPdQFuaei0FkuiTNyw1qRJn8NpBDIawfB3E,16499
max_client/api_client.py,sha256=sIF-_GsXESlowGalUVcKS4PElxBdwdb-GglgUR8_mSg,34357
max_client/cmn.py,sha256=d7FK09mbgTPAJ1Y_FUnObaGgH8ninNzt_HnD6NirepM,14281
max_client/configuration.py,sha256=f3qptvGdoTxT4OrXlH6sr5Irbyuv0XzAObwdia0haew,16411
max_client/max_chat.py,sha256=sXrso_s7tX9XugGslcuGZFy9LnnydwYT-FloZ7iIovw,3289
max_client/max_commander.py,sha256=72YMh7SDod3YZ8gE68p8AujzFd8TpIL6bL12l00nwhI,7390
max_client/max_editmessage.py,sha256=DATfnwtmxNHe7BNQlc5keO-klXCC7oumVuMlzdma5eg,55559
max_client/max_repiter.py,sha256=OxDfYT5agk5QE9s_iA436ybYz6yGiLNj8665KROhpIE,1234
max_client/models/__init__.py,sha256=GXfHUVvb3tOCLVww2uddpNde5jebxJpCuk7XjiDaUJA,16971
max_client/models/__pycache__/__init__.cpython-312.pyc,,
max_client/models/__pycache__/action_request_body.cpython-312.pyc,,
max_client/models/__pycache__/attachment.cpython-312.pyc,,
max_client/models/__pycache__/attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/audio_attachment.cpython-312.pyc,,
max_client/models/__pycache__/audio_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/bot_added_to_chat_update.cpython-312.pyc,,
max_client/models/__pycache__/bot_command.cpython-312.pyc,,
max_client/models/__pycache__/bot_info.cpython-312.pyc,,
max_client/models/__pycache__/bot_patch.cpython-312.pyc,,
max_client/models/__pycache__/bot_removed_from_chat_update.cpython-312.pyc,,
max_client/models/__pycache__/bot_started_update.cpython-312.pyc,,
max_client/models/__pycache__/button.cpython-312.pyc,,
max_client/models/__pycache__/callback.cpython-312.pyc,,
max_client/models/__pycache__/callback_answer.cpython-312.pyc,,
max_client/models/__pycache__/callback_button.cpython-312.pyc,,
max_client/models/__pycache__/chat.cpython-312.pyc,,
max_client/models/__pycache__/chat_admin.cpython-312.pyc,,
max_client/models/__pycache__/chat_admin_permission.cpython-312.pyc,,
max_client/models/__pycache__/chat_admins_list.cpython-312.pyc,,
max_client/models/__pycache__/chat_button.cpython-312.pyc,,
max_client/models/__pycache__/chat_list.cpython-312.pyc,,
max_client/models/__pycache__/chat_member.cpython-312.pyc,,
max_client/models/__pycache__/chat_members_list.cpython-312.pyc,,
max_client/models/__pycache__/chat_patch.cpython-312.pyc,,
max_client/models/__pycache__/chat_status.cpython-312.pyc,,
max_client/models/__pycache__/chat_title_changed_update.cpython-312.pyc,,
max_client/models/__pycache__/chat_type.cpython-312.pyc,,
max_client/models/__pycache__/contact_attachment.cpython-312.pyc,,
max_client/models/__pycache__/contact_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/contact_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/contact_attachment_request_payload.cpython-312.pyc,,
max_client/models/__pycache__/data_attachment.cpython-312.pyc,,
max_client/models/__pycache__/emphasized_markup.cpython-312.pyc,,
max_client/models/__pycache__/error.cpython-312.pyc,,
max_client/models/__pycache__/file_attachment.cpython-312.pyc,,
max_client/models/__pycache__/file_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/file_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/get_pinned_message_result.cpython-312.pyc,,
max_client/models/__pycache__/get_subscriptions_result.cpython-312.pyc,,
max_client/models/__pycache__/heading_markup.cpython-312.pyc,,
max_client/models/__pycache__/highlighted_markup.cpython-312.pyc,,
max_client/models/__pycache__/image.cpython-312.pyc,,
max_client/models/__pycache__/inline_keyboard_attachment.cpython-312.pyc,,
max_client/models/__pycache__/inline_keyboard_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/inline_keyboard_attachment_request_payload.cpython-312.pyc,,
max_client/models/__pycache__/intent.cpython-312.pyc,,
max_client/models/__pycache__/keyboard.cpython-312.pyc,,
max_client/models/__pycache__/link_button.cpython-312.pyc,,
max_client/models/__pycache__/link_markup.cpython-312.pyc,,
max_client/models/__pycache__/linked_message.cpython-312.pyc,,
max_client/models/__pycache__/location_attachment.cpython-312.pyc,,
max_client/models/__pycache__/location_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/markup_element.cpython-312.pyc,,
max_client/models/__pycache__/media_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/message.cpython-312.pyc,,
max_client/models/__pycache__/message_body.cpython-312.pyc,,
max_client/models/__pycache__/message_button.cpython-312.pyc,,
max_client/models/__pycache__/message_callback_update.cpython-312.pyc,,
max_client/models/__pycache__/message_chat_created_update.cpython-312.pyc,,
max_client/models/__pycache__/message_created_update.cpython-312.pyc,,
max_client/models/__pycache__/message_edited_update.cpython-312.pyc,,
max_client/models/__pycache__/message_link_type.cpython-312.pyc,,
max_client/models/__pycache__/message_list.cpython-312.pyc,,
max_client/models/__pycache__/message_removed_update.cpython-312.pyc,,
max_client/models/__pycache__/message_stat.cpython-312.pyc,,
max_client/models/__pycache__/monospaced_markup.cpython-312.pyc,,
max_client/models/__pycache__/new_message_body.cpython-312.pyc,,
max_client/models/__pycache__/new_message_link.cpython-312.pyc,,
max_client/models/__pycache__/photo_attachment.cpython-312.pyc,,
max_client/models/__pycache__/photo_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/photo_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/photo_attachment_request_payload.cpython-312.pyc,,
max_client/models/__pycache__/photo_token.cpython-312.pyc,,
max_client/models/__pycache__/photo_tokens.cpython-312.pyc,,
max_client/models/__pycache__/pin_message_body.cpython-312.pyc,,
max_client/models/__pycache__/recipient.cpython-312.pyc,,
max_client/models/__pycache__/reply_button.cpython-312.pyc,,
max_client/models/__pycache__/reply_keyboard_attachment.cpython-312.pyc,,
max_client/models/__pycache__/reply_keyboard_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/request_contact_button.cpython-312.pyc,,
max_client/models/__pycache__/request_geo_location_button.cpython-312.pyc,,
max_client/models/__pycache__/send_contact_button.cpython-312.pyc,,
max_client/models/__pycache__/send_geo_location_button.cpython-312.pyc,,
max_client/models/__pycache__/send_message_button.cpython-312.pyc,,
max_client/models/__pycache__/send_message_result.cpython-312.pyc,,
max_client/models/__pycache__/sender_action.cpython-312.pyc,,
max_client/models/__pycache__/share_attachment.cpython-312.pyc,,
max_client/models/__pycache__/share_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/share_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/simple_query_result.cpython-312.pyc,,
max_client/models/__pycache__/sticker_attachment.cpython-312.pyc,,
max_client/models/__pycache__/sticker_attachment_payload.cpython-312.pyc,,
max_client/models/__pycache__/sticker_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/sticker_attachment_request_payload.cpython-312.pyc,,
max_client/models/__pycache__/strikethrough_markup.cpython-312.pyc,,
max_client/models/__pycache__/strong_markup.cpython-312.pyc,,
max_client/models/__pycache__/subscription.cpython-312.pyc,,
max_client/models/__pycache__/subscription_request_body.cpython-312.pyc,,
max_client/models/__pycache__/text_format.cpython-312.pyc,,
max_client/models/__pycache__/underline_markup.cpython-312.pyc,,
max_client/models/__pycache__/update.cpython-312.pyc,,
max_client/models/__pycache__/update_list.cpython-312.pyc,,
max_client/models/__pycache__/upload_endpoint.cpython-312.pyc,,
max_client/models/__pycache__/upload_type.cpython-312.pyc,,
max_client/models/__pycache__/uploaded_info.cpython-312.pyc,,
max_client/models/__pycache__/user.cpython-312.pyc,,
max_client/models/__pycache__/user_added_to_chat_update.cpython-312.pyc,,
max_client/models/__pycache__/user_ids_list.cpython-312.pyc,,
max_client/models/__pycache__/user_mention_markup.cpython-312.pyc,,
max_client/models/__pycache__/user_removed_from_chat_update.cpython-312.pyc,,
max_client/models/__pycache__/user_with_photo.cpython-312.pyc,,
max_client/models/__pycache__/video_attachment.cpython-312.pyc,,
max_client/models/__pycache__/video_attachment_details.cpython-312.pyc,,
max_client/models/__pycache__/video_attachment_request.cpython-312.pyc,,
max_client/models/__pycache__/video_thumbnail.cpython-312.pyc,,
max_client/models/__pycache__/video_urls.cpython-312.pyc,,
max_client/models/action_request_body.py,sha256=jzAiXh9mdVc7F9pY7Wjl9VVEk92vtVQtdZlViseJFds,11918
max_client/models/attachment.py,sha256=IMHafm6XpK5uIgXyIfr5m8asTcBmZsNvTc_lNj9AYKM,12487
max_client/models/attachment_payload.py,sha256=eRqeq70o_JPKT90HdP5mt2bAwYcYNO7p6mGgwTv-i3M,12162
max_client/models/attachment_request.py,sha256=AKR8I14SHrKwKMWNiGCgAQRmFrCDssqy3JE11JyRvp0,12597
max_client/models/audio_attachment.py,sha256=LvkTyFihiayDnTWTynFSuKF1xKP26AuP8oawluGLZjA,12105
max_client/models/audio_attachment_request.py,sha256=-u8mzwTgyPSZCmEyA7LfCKAFLr36yWyy8iBCHYC8Mds,12153
max_client/models/bot_added_to_chat_update.py,sha256=I2GqYqNtLe5BGInPlcdoIKyD_8RsCb-yFvG4Jq3QUqo,14072
max_client/models/bot_command.py,sha256=6bHsIrkPrPYg0A_57U1Xb2pPbUzp5UPL-kJl_w_S5H8,13335
max_client/models/bot_info.py,sha256=HmBIu9HUt5x7ZrnqpR9bnv7vdsPSUm3jx3n-wpJDfRc,12684
max_client/models/bot_patch.py,sha256=KhaJ8GqCZ6mjoM-A68ZWlqY9xTuUq3jJjc9DM7vMlrA,16066
max_client/models/bot_removed_from_chat_update.py,sha256=nZzg8f64oWQGKdy_rw-IyGK6FsXuAo-uDaFFLlAm37A,14164
max_client/models/bot_started_update.py,sha256=T7rdGegXZHbbKhD6R-QpSiqpjKltsVpT5DJSwX6mwMo,14892
max_client/models/button.py,sha256=XwO8VSocfSlghwqqtSiJoP-W4vkjX_Cf_io_s3sDSIo,13430
max_client/models/callback.py,sha256=8xMNlMN5_hcTc3z-3fJUmBkQ45u-hi9w0D0mMBgOxsg,14338
max_client/models/callback_answer.py,sha256=b8qlUegUfKIhTKGRzOdiTmDZ0Ca0mSu68XVh5XyudIE,12820
max_client/models/callback_button.py,sha256=wQbSWfqNVgfEU4x63hiXT9BpjOi8Kv_szYXX8Pmk8ps,13157
max_client/models/chat.py,sha256=sQqauLrVYUcpt5bJoLq1eMx7nmid9xPyikANqG64TiA,24756
max_client/models/chat_admin.py,sha256=u6TPwX1rpF9gKnr2rs0bf_uKNlhtSlHVQkaXB6bekcA,13440
max_client/models/chat_admin_permission.py,sha256=fN2tmWL6BEETElnwe5nf5LQgqYqYoz2tX2Ib-mQPxtc,11430
max_client/models/chat_admins_list.py,sha256=-SVKEw1SuNxMPuDwJgTD4lGApwDTuL2b-CThYhAbezw,11906
max_client/models/chat_button.py,sha256=0-LCCiLhwEDuPV50IMRlbi3sqGxIyL7-_GxrYevEZqg,15833
max_client/models/chat_list.py,sha256=SJIYZpn_eOu_zURmLyGjM0u5uJh7XMblLcVQKn-aGsc,12622
max_client/models/chat_member.py,sha256=BXeYpMa4NJABpnsee_yzPgyJ99OO2y2mHkQnHJP_3LI,17093
max_client/models/chat_members_list.py,sha256=ZkKwecEydnR4L6Ev2CQpzgpUYfNz1CQ1Eqtc5sKpkuY,12835
max_client/models/chat_patch.py,sha256=rt-ukjAY20YVei5DEQtcz6A6j5cgItjdzzfDVUMsFdc,14235
max_client/models/chat_status.py,sha256=6ot_YOv_Ahbmh3lOv_v5ajzH316FVn1B-RGjZCRORCs,11303
max_client/models/chat_title_changed_update.py,sha256=3durH-EyS_aiUSDeCilEn4mKrRVGn1BY0SS1NGBNt4c,13934
max_client/models/chat_type.py,sha256=zI5PaqhGaULRvxMpFaX-Jf3mpX9oTgGTLZX_mrW70BE,11247
max_client/models/contact_attachment.py,sha256=kIZCSafr3UIM698lMzOOHp-AuMVwqKUXbd6LS896dQc,12130
max_client/models/contact_attachment_payload.py,sha256=SWEW5OfFnUkAx4x3P4hZAxKtdgZGRpkGqwAwcvmT5us,12685
max_client/models/contact_attachment_request.py,sha256=3c7BHz9AV8UFIpr5LE-0N99J_ktKfeRbn5gJk41liok,12229
max_client/models/contact_attachment_request_payload.py,sha256=GVY1sEn8HveT4gHDWReBuq2T-xYKyjlXCmZ0aYysIvE,14278
max_client/models/data_attachment.py,sha256=2V_tJOJCEQ4_B8ZjbeMoMqLKJ3vwEneE5XQEi7kOEdk,11828
max_client/models/emphasized_markup.py,sha256=ylRrWURAx-7fIuHbAfTPxbe6Wye10K6LaXbJIkk1b9Q,11850
max_client/models/error.py,sha256=htewmHiTDAqK13qz5-y6hNC1IEYCmTx68dhYVLpFK_I,13249
max_client/models/file_attachment.py,sha256=Orpaz0ppEBppTuXzHTvVon7w0mFgQLdYDYrOsooGVig,13679
max_client/models/file_attachment_payload.py,sha256=VKcv0gKYdHZVxvZsPUz35Onhavf8tCL4u0hsfGpkrMI,12286
max_client/models/file_attachment_request.py,sha256=c99wUgTVzjjsaWM9Rrye76YExRJTEHBKvL-c4BZD-6g,12144
max_client/models/get_pinned_message_result.py,sha256=o7H6RWwIWzUYFJJZwDxNiKIiM0AleIMLHY__AdWOVoI,11999
max_client/models/get_subscriptions_result.py,sha256=gRglk-m00sSmes-ox51DSIUfd-4SiTy1NSr7LrBiJe0,12206
max_client/models/heading_markup.py,sha256=BNbqkekvHcfP-N96z49fBOJZnfjtIc3o_CNLGxmqptQ,11836
max_client/models/highlighted_markup.py,sha256=Vl0yHlQqWYLOtMobPEWtVo_WX8Fr1piOSRUi51glIUg,11862
max_client/models/image.py,sha256=AT7iCQV0z3CJNZuTSYtJ2sYWOdq-RsjWmOt8ANRlwH8,11814
max_client/models/inline_keyboard_attachment.py,sha256=z5hce6MNJ5EzvR-EDJID4Pg3xHtWtUYiTnT4QaNaQ0s,12145
max_client/models/inline_keyboard_attachment_request.py,sha256=5Qbyh9eZ0mlkNjhDLCmTs3l2txIjd7X0Ez2HEIxxYeA,12314
max_client/models/inline_keyboard_attachment_request_payload.py,sha256=sNiQSUFf5NGhOHKwSaeLpYhSNqH1PXoftCKgtsMguv8,12214
max_client/models/intent.py,sha256=6nm4us3S_QFjdxYMWskDIASYJOU6ANGgJ09jJYN-FwU,11253
max_client/models/keyboard.py,sha256=aUprBqpNHyj_KNL6p57P2AumgkpxGq6Z_YUikDm00JE,11894
max_client/models/link_button.py,sha256=EygRlKG3W0yW26n_73vw2hmF86mVJYNAgZL3OazgIeI,12137
max_client/models/link_markup.py,sha256=m8B_orY6Dgh-0-ZRiycj6zzo8frW11HmvgXxTUc6mXQ,12868
max_client/models/linked_message.py,sha256=T-iwR6Q1WX7-cYo2yyNO4aAJQ12Tfjfuwx-lO87oD1U,14469
max_client/models/location_attachment.py,sha256=cYyrW0mwC3uMCgORgD20Gfh9qRF4TaNFmOoj-20q5Vc,12897
max_client/models/location_attachment_request.py,sha256=z4XHx-zyPLojHAprPDpY8_KenVQDcdiaLMgBSOyp5Go,13003
max_client/models/markup_element.py,sha256=E886A4d_Iqu0SSL6cDwUvjgXKyKM55uvVPhJRcrE58M,18921
max_client/models/media_attachment_payload.py,sha256=FRNzDV0gbDMqI9jlr5-foONnRd8EFWL9Nfbd1bcQqRA,12294
max_client/models/message.py,sha256=1wte6O94MmE7xHd5v5o0DqF_2JLwb8ZnfxMUvss9Ycc,17550
max_client/models/message_body.py,sha256=SBAOrnVBYO1nhUnbACy0Wqg_mqUdPUWWjoaXOPjp0XA,15889
max_client/models/message_button.py,sha256=VkJ3rXeH9rmJGQvtIpXydUwkK7u7JDeofS8M6gTJKw8,11157
max_client/models/message_callback_update.py,sha256=CfgfLCImiEQ5kvNjV29szCv9QLlg39lho7EtcAs0OaI,14035
max_client/models/message_chat_created_update.py,sha256=Ci4PiirhyeR-XwCl23jZ73TcBOhVYP2rJYnig_j8s2M,14044
max_client/models/message_created_update.py,sha256=3jQLQMfmOi7IRYn51f_NCsCsAUQK9TiN_Yi4ELwuiHc,13202
max_client/models/message_edited_update.py,sha256=QQI-10r1J1f71WTqeRUBgBqaWoT-jpljVBd0_ZcQcdo,12287
max_client/models/message_link_type.py,sha256=4QLQO0J-OKr-Ei9fm8OTrdHBknpNIgLKDla0qETsDsA,11248
max_client/models/message_list.py,sha256=aZUqYPqHHU1yIpG-us7YOUdn1e0MOiAUuCBximaaLSk,11999
max_client/models/message_removed_update.py,sha256=1w84skP3A_nGXY8QwDF_JJCeSQ_CRNWzWARk9Ardp8A,14129
max_client/models/message_stat.py,sha256=UMzLgwtKJ8MQwm4krnUNQX-mKxHhZbdmLyw3ta6jg64,11828
max_client/models/monospaced_markup.py,sha256=dD1bTV53l1y1f1T3TMv-LuurA6dEl9XaiPlu3uPfyf8,11859
max_client/models/new_message_body.py,sha256=HTOIWpFPukUuRt2SmVU8Gr0ovSAd9sge4kD9kP0LrTE,15188
max_client/models/new_message_link.py,sha256=ORpyv3XnKsQGOpI9FwPPQBrHoc-tE-roE1u4JmxAuuY,12719
max_client/models/photo_attachment.py,sha256=ml1-DNKiQtWjMGklfEk_8WWUANOqr413FMDLrWXW-eg,12106
max_client/models/photo_attachment_payload.py,sha256=VwLlytYxLvatQnZw-ohziBoX7DK0rMBgFgN2VZqP6ng,13705
max_client/models/photo_attachment_request.py,sha256=G6eZdLaoZowSL9RrAe4qeduVcqtilcLj1zgmg83-G8s,12204
max_client/models/photo_attachment_request_payload.py,sha256=fgBw29hzi0qSNHO9iLU7vXgA45RKY8bVSaDwqg9kcoA,13663
max_client/models/photo_token.py,sha256=KWmZHquAk4avu-MysOfqT9HdpuYZNzNwmXykz4aas4A,11941
max_client/models/photo_tokens.py,sha256=zLZL4w6cC04oPTA3-of8CIkakyJlyUs4d-MNn3_zkm4,11903
max_client/models/pin_message_body.py,sha256=3ASdMR8nxUAVt1lhYQPor6ZQMWZIo-epJiSKHvcJxV0,12875
max_client/models/recipient.py,sha256=zsWTgE6dgJ_R5dHtnye1ocVji6NYEPgNNwJzir4cXo8,13365
max_client/models/reply_button.py,sha256=MGnP2Gqcs_MFNH1TH7IglyBLsslTIp9jrWvdVatOok0,13556
max_client/models/reply_keyboard_attachment.py,sha256=2Lka9W5GWoMVBWeKNTw3iJJd_0PDvAs2xsIE1ybTiiE,12014
max_client/models/reply_keyboard_attachment_request.py,sha256=FG8hWfufeGhHizsyekbkHzC1cAZm92D624yJ8scHboE,14161
max_client/models/request_contact_button.py,sha256=FJiqzcxj_9jBNFJM17O5gF0bnPL8_cqbi5GtVCXpGjg,11397
max_client/models/request_geo_location_button.py,sha256=qwu6GXi8XCUNhbs-ixSAhKQTRR237Rjhn0OGp6bGQ_k,12233
max_client/models/send_contact_button.py,sha256=dKKP1ySnLfEaVvp48PtDR6-gHgkNYWIH3Gcs-lDrkGo,11169
max_client/models/send_geo_location_button.py,sha256=IbJXElw8vNHOyKLaM5-mvntoO7I9paChm8NbHrgv7rc,11984
max_client/models/send_message_button.py,sha256=NO9ytNo4Pxh9XFd7SY_Bq3RiOYJdi6mnAZwtSGIDrcA,11957
max_client/models/send_message_result.py,sha256=47Mj-macURetFgnGtvR4z_WkQnibWRpljZld1fcFs18,11924
max_client/models/sender_action.py,sha256=qi-auIJ7P9BAlXSyaYMgxEiy7zPo4QY6FK2_Ozlqwn4,11393
max_client/models/share_attachment.py,sha256=MN7MPWvJPVkAe5rrUEUSbL94lvDLJcxXfTO9JH3gpf8,14255
max_client/models/share_attachment_payload.py,sha256=0T61Wn4SgB-ypcTIAtsjSV55wbjJrj4FVZMCm75a928,12716
max_client/models/share_attachment_request.py,sha256=9GnXavAw53gfyOBXNQASN4wy8BFzfksEh11kqsIEx0o,12183
max_client/models/simple_query_result.py,sha256=ryUs047UO7DYUxTCMA_1XVKy_HsGygZyBX8wpT_1sto,12868
max_client/models/sticker_attachment.py,sha256=Zt3dP6A8OoK5J99tklrc-mATWMhN3lTTS4uQIO5v8GY,13698
max_client/models/sticker_attachment_payload.py,sha256=PxnxFH9ZliAkuWAmWI4vEvyp2O7cDKRGGC0sOS7hWC4,11980
max_client/models/sticker_attachment_request.py,sha256=Fp5ztRBiNx8xnHAUvhAjrF4Bcwxv6jwgwY-gp8U-5xI,12228
max_client/models/sticker_attachment_request_payload.py,sha256=C227iholyFfCShJskt5hDo_lKcK8GRpD3FyHYQIz_Z8,12017
max_client/models/strikethrough_markup.py,sha256=vgF4qQo-K1PC65Riy5_A1LKWoxMUH7Ve791Rp2JfIHM,11866
max_client/models/strong_markup.py,sha256=cU7e7yS7DArzLC5UYpBCiyZ8Y_-M3i-ATT0CbxBho4k,11831
max_client/models/subscription.py,sha256=YRgwGbyLyegp6dH4g3Mk604V21Nw1Sk8oQqmlPkneAg,14335
max_client/models/subscription_request_body.py,sha256=87o0EZErxYJmO8CHJgjsah4pboaiWjR-2GZuY8fPSHw,13988
max_client/models/text_format.py,sha256=Lm8GeOUyUla5Sl2ptpkfpVw9gHMjp4UcR10qVYeC9yQ,11233
max_client/models/underline_markup.py,sha256=9Uxm6MN-eF_5q5W2Bu5W97R16Ta9LY6WidfyKj1pXSs,11846
max_client/models/update.py,sha256=n9bWhs1p2aiaF4xsL8ps2stn4eDbCA1NrYO3EtjIJNo,14179
max_client/models/update_list.py,sha256=hBtNXb2bfZtpk-WrCboMIs5KSiFQVWf4-ZVjdvTbhdw,12644
max_client/models/upload_endpoint.py,sha256=UmvGQbkr1oi-U797tv0slUJyyr-qSwKUVo9VQPBM0tM,12630
max_client/models/upload_type.py,sha256=ifkpd2Q5aGWFckrU1JSzC3gxiZxGKaXer7hoiGy5_uk,11312
max_client/models/uploaded_info.py,sha256=vsQ2FLQ7IkJypv-4OfOyQ1QkICCCgbZ3StIWFheMtv0,11879
max_client/models/user.py,sha256=MlMjDihfsf1ibXSDinoEjeEIYghoBoIigCEyZX61t0c,17060
max_client/models/user_added_to_chat_update.py,sha256=lhC2evh-18QhOEDTwZ2Kl29dcgDcYRFdhQFDHXQvr7k,15001
max_client/models/user_ids_list.py,sha256=feaVqQsZ9dn8vV6RVvwEIJfRFFuSnJKHLCdMz0Mx4hc,11909
max_client/models/user_mention_markup.py,sha256=pKWyjLxoRxD5k4Of4MfYBB9VhGz4EDJ3Q93opSZBTv0,13434
max_client/models/user_removed_from_chat_update.py,sha256=dooECabx-uLkKh1tbnSHx-9Gn6Fo4VHVFaa4laVIQ1E,15104
max_client/models/user_with_photo.py,sha256=jmR0fIVo07dJchR4-LznXm1jI2aYvDRwJ3v9tN-51XE,14557
max_client/models/video_attachment.py,sha256=1ucIAdx3k2MWJYeFskOgqS8HlYj2sVBjxC79_wLTYls,14801
max_client/models/video_attachment_details.py,sha256=L11XIeiiLs3V6DPW0ouCKLhddOOziMNHToRyNdP1Rk4,16141
max_client/models/video_attachment_request.py,sha256=UiFZ9HrQn2WoSTkn4JnAlBPdHf1p9E3GsyfWhgQuNeE,12153
max_client/models/video_thumbnail.py,sha256=0hfzgHWIBKbADlDM6WFRm0GnXGcehV8bY_U9eQuLuNs,11871
max_client/models/video_urls.py,sha256=1EzcWdCRJ1ROMvOdkHFXMTqh1vw8wCqzTF26V-Wp9LI,16342
max_client/rest.py,sha256=AUMYq-eIFrVKF7Mtk0puKztRP104t7UPIKjYNqPPIUM,22174
max_client/utl.py,sha256=5ilfFtj8BrOkrFzyrrclltZFgzw-LNLtBTHTgKM77xU,655
test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test/__pycache__/__init__.cpython-312.pyc,,
test/__pycache__/test_action_request_body.cpython-312.pyc,,
test/__pycache__/test_attachment.cpython-312.pyc,,
test/__pycache__/test_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_attachment_request.cpython-312.pyc,,
test/__pycache__/test_audio_attachment.cpython-312.pyc,,
test/__pycache__/test_audio_attachment_request.cpython-312.pyc,,
test/__pycache__/test_bot_added_to_chat_update.cpython-312.pyc,,
test/__pycache__/test_bot_command.cpython-312.pyc,,
test/__pycache__/test_bot_info.cpython-312.pyc,,
test/__pycache__/test_bot_patch.cpython-312.pyc,,
test/__pycache__/test_bot_removed_from_chat_update.cpython-312.pyc,,
test/__pycache__/test_bot_started_update.cpython-312.pyc,,
test/__pycache__/test_bots_api.cpython-312.pyc,,
test/__pycache__/test_button.cpython-312.pyc,,
test/__pycache__/test_callback.cpython-312.pyc,,
test/__pycache__/test_callback_answer.cpython-312.pyc,,
test/__pycache__/test_callback_button.cpython-312.pyc,,
test/__pycache__/test_chat.cpython-312.pyc,,
test/__pycache__/test_chat_admin.cpython-312.pyc,,
test/__pycache__/test_chat_admin_permission.cpython-312.pyc,,
test/__pycache__/test_chat_admins_list.cpython-312.pyc,,
test/__pycache__/test_chat_button.cpython-312.pyc,,
test/__pycache__/test_chat_list.cpython-312.pyc,,
test/__pycache__/test_chat_member.cpython-312.pyc,,
test/__pycache__/test_chat_members_list.cpython-312.pyc,,
test/__pycache__/test_chat_patch.cpython-312.pyc,,
test/__pycache__/test_chat_status.cpython-312.pyc,,
test/__pycache__/test_chat_title_changed_update.cpython-312.pyc,,
test/__pycache__/test_chat_type.cpython-312.pyc,,
test/__pycache__/test_chats_api.cpython-312.pyc,,
test/__pycache__/test_contact_attachment.cpython-312.pyc,,
test/__pycache__/test_contact_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_contact_attachment_request.cpython-312.pyc,,
test/__pycache__/test_contact_attachment_request_payload.cpython-312.pyc,,
test/__pycache__/test_data_attachment.cpython-312.pyc,,
test/__pycache__/test_emphasized_markup.cpython-312.pyc,,
test/__pycache__/test_error.cpython-312.pyc,,
test/__pycache__/test_file_attachment.cpython-312.pyc,,
test/__pycache__/test_file_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_file_attachment_request.cpython-312.pyc,,
test/__pycache__/test_get_pinned_message_result.cpython-312.pyc,,
test/__pycache__/test_get_subscriptions_result.cpython-312.pyc,,
test/__pycache__/test_heading_markup.cpython-312.pyc,,
test/__pycache__/test_highlighted_markup.cpython-312.pyc,,
test/__pycache__/test_image.cpython-312.pyc,,
test/__pycache__/test_inline_keyboard_attachment.cpython-312.pyc,,
test/__pycache__/test_inline_keyboard_attachment_request.cpython-312.pyc,,
test/__pycache__/test_inline_keyboard_attachment_request_payload.cpython-312.pyc,,
test/__pycache__/test_intent.cpython-312.pyc,,
test/__pycache__/test_keyboard.cpython-312.pyc,,
test/__pycache__/test_link_button.cpython-312.pyc,,
test/__pycache__/test_link_markup.cpython-312.pyc,,
test/__pycache__/test_linked_message.cpython-312.pyc,,
test/__pycache__/test_location_attachment.cpython-312.pyc,,
test/__pycache__/test_location_attachment_request.cpython-312.pyc,,
test/__pycache__/test_markup_element.cpython-312.pyc,,
test/__pycache__/test_media_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_message.cpython-312.pyc,,
test/__pycache__/test_message_body.cpython-312.pyc,,
test/__pycache__/test_message_button.cpython-312.pyc,,
test/__pycache__/test_message_callback_update.cpython-312.pyc,,
test/__pycache__/test_message_chat_created_update.cpython-312.pyc,,
test/__pycache__/test_message_created_update.cpython-312.pyc,,
test/__pycache__/test_message_edited_update.cpython-312.pyc,,
test/__pycache__/test_message_link_type.cpython-312.pyc,,
test/__pycache__/test_message_list.cpython-312.pyc,,
test/__pycache__/test_message_removed_update.cpython-312.pyc,,
test/__pycache__/test_message_stat.cpython-312.pyc,,
test/__pycache__/test_messages_api.cpython-312.pyc,,
test/__pycache__/test_monospaced_markup.cpython-312.pyc,,
test/__pycache__/test_new_message_body.cpython-312.pyc,,
test/__pycache__/test_new_message_link.cpython-312.pyc,,
test/__pycache__/test_photo_attachment.cpython-312.pyc,,
test/__pycache__/test_photo_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_photo_attachment_request.cpython-312.pyc,,
test/__pycache__/test_photo_attachment_request_payload.cpython-312.pyc,,
test/__pycache__/test_photo_token.cpython-312.pyc,,
test/__pycache__/test_photo_tokens.cpython-312.pyc,,
test/__pycache__/test_pin_message_body.cpython-312.pyc,,
test/__pycache__/test_recipient.cpython-312.pyc,,
test/__pycache__/test_reply_button.cpython-312.pyc,,
test/__pycache__/test_reply_keyboard_attachment.cpython-312.pyc,,
test/__pycache__/test_reply_keyboard_attachment_request.cpython-312.pyc,,
test/__pycache__/test_request_contact_button.cpython-312.pyc,,
test/__pycache__/test_request_geo_location_button.cpython-312.pyc,,
test/__pycache__/test_send_contact_button.cpython-312.pyc,,
test/__pycache__/test_send_geo_location_button.cpython-312.pyc,,
test/__pycache__/test_send_message_button.cpython-312.pyc,,
test/__pycache__/test_send_message_result.cpython-312.pyc,,
test/__pycache__/test_sender_action.cpython-312.pyc,,
test/__pycache__/test_share_attachment.cpython-312.pyc,,
test/__pycache__/test_share_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_share_attachment_request.cpython-312.pyc,,
test/__pycache__/test_simple_query_result.cpython-312.pyc,,
test/__pycache__/test_sticker_attachment.cpython-312.pyc,,
test/__pycache__/test_sticker_attachment_payload.cpython-312.pyc,,
test/__pycache__/test_sticker_attachment_request.cpython-312.pyc,,
test/__pycache__/test_sticker_attachment_request_payload.cpython-312.pyc,,
test/__pycache__/test_strikethrough_markup.cpython-312.pyc,,
test/__pycache__/test_strong_markup.cpython-312.pyc,,
test/__pycache__/test_subscription.cpython-312.pyc,,
test/__pycache__/test_subscription_request_body.cpython-312.pyc,,
test/__pycache__/test_subscriptions_api.cpython-312.pyc,,
test/__pycache__/test_text_format.cpython-312.pyc,,
test/__pycache__/test_underline_markup.cpython-312.pyc,,
test/__pycache__/test_update.cpython-312.pyc,,
test/__pycache__/test_update_list.cpython-312.pyc,,
test/__pycache__/test_upload_api.cpython-312.pyc,,
test/__pycache__/test_upload_endpoint.cpython-312.pyc,,
test/__pycache__/test_upload_type.cpython-312.pyc,,
test/__pycache__/test_uploaded_info.cpython-312.pyc,,
test/__pycache__/test_user.cpython-312.pyc,,
test/__pycache__/test_user_added_to_chat_update.cpython-312.pyc,,
test/__pycache__/test_user_ids_list.cpython-312.pyc,,
test/__pycache__/test_user_mention_markup.cpython-312.pyc,,
test/__pycache__/test_user_removed_from_chat_update.cpython-312.pyc,,
test/__pycache__/test_user_with_photo.cpython-312.pyc,,
test/__pycache__/test_video_attachment.cpython-312.pyc,,
test/__pycache__/test_video_attachment_details.cpython-312.pyc,,
test/__pycache__/test_video_attachment_request.cpython-312.pyc,,
test/__pycache__/test_video_thumbnail.cpython-312.pyc,,
test/__pycache__/test_video_urls.cpython-312.pyc,,
test/test_action_request_body.py,sha256=wLclIqdr1hSNsfDJ5s8nfDGYTr6iLNyT5WEMP7aeYW0,9880
test/test_attachment.py,sha256=jqzDgPsA1DYknki1NDjZsgQ5c2qwfODdI2V1-B_3-5c,9820
test/test_attachment_payload.py,sha256=M1w1kWHdpafI94HJPTHXOCcvlOoj_2yh6bDCajWDJyA,9878
test/test_attachment_request.py,sha256=XtnGuiB3SWWz9WvXt-YtbYAgLguMfmQgu5yjNK0KmIs,9878
test/test_audio_attachment.py,sha256=nMT-Kq0ALRFveEBG-xqfmC2brxs4flc5UxDVNzFetV4,9862
test/test_audio_attachment_request.py,sha256=RyttmunXUF3MIP4MLL6i47ST8IVCx_fKl1QhX15Hwa0,9920
test/test_bot_added_to_chat_update.py,sha256=bUQUBTVxZYfXgwxptiteHJyO-G9Fa-LN5bazvhHPg84,9908
test/test_bot_command.py,sha256=ZT6UlDoQc8LPt98a_-aHBhotQvizRSXABsBWGPYjzy4,9822
test/test_bot_info.py,sha256=7tAQoU7l0Ahmrc54OuaXTq45dQSYN7kaZAqcXS2Imj8,9798
test/test_bot_patch.py,sha256=iLCfSYeMgwxnWGsSGOyQUYWOVJ7odADd4hZytsy8gxs,9806
test/test_bot_removed_from_chat_update.py,sha256=y8OTTd3Hc-zmKKzgDZtGLPob_Q65z9ed07T3C2UR-ik,9940
test/test_bot_started_update.py,sha256=7DBtvT45sSKYTfejdLcUkyOWG_jWSKPEKg4LiaI1NfU,9872
test/test_bots_api.py,sha256=uDRDJYOgN-Bk586v-eLstRRXmm9Vdb5rtCz1M0ADNkM,9913
test/test_button.py,sha256=sdbEGMajAWkYrHa9KVy7uYyDcX9LefkQIunUnyOci-o,9788
test/test_callback.py,sha256=C-uawUtoULGZ5r8yTj81s_9CrKfpuzEi5rZyY7IAoUs,9804
test/test_callback_answer.py,sha256=eQNYojT8Lj7OSTyBJUI00hOh_5XJgCn5J1eH0HLwTnI,9854
test/test_callback_button.py,sha256=Y5dJWhzGvTwj0c0XnXO4gguq32ebozePHE7mw3FhK0E,9854
test/test_chat.py,sha256=Ec39XmqSO4KyrrzsXXq64rS6TboY17ExRvheiKtYmXo,9772
test/test_chat_admin.py,sha256=4dZdX1QgL--Hhs1Iy_ASmjqmEzp4DpRTvX48dU2IIHM,9814
test/test_chat_admin_permission.py,sha256=f3nBUJdQrnVkTefS4BgX3Nlnmewj9dMTFEF6YOzf98I,9896
test/test_chat_admins_list.py,sha256=2F7QsEcwP8apqNcaqyzBGCHlC0svpXDLAM9djuLvtoQ,9856
test/test_chat_button.py,sha256=FCJ0ZBh2SV2F1Cyv9vn9q3KDfmJC9Euq6_7G8SLP6tA,9822
test/test_chat_list.py,sha256=JmBxwLLXsqH-i7MA_qsFd2Z97Aoo4tBQujFdCUhUT9Q,9806
test/test_chat_member.py,sha256=RTK1lMy-M7kn_E7fvWytAzThsPD3qp_Jq3sI-cSrWkc,9822
test/test_chat_members_list.py,sha256=GjtC82jcwtmJruTyPIKTSDqs6HpiTml3jSIK7F_T5G4,9864
test/test_chat_patch.py,sha256=HRp6TNQtD_KaDS1bpwMIhzTV3wo0OE6binR2C8M67hc,9814
test/test_chat_status.py,sha256=cyz_BbQbZLTOAtEtSUgwdUjP_gnLgXAoX40xqOOKnJM,9822
test/test_chat_title_changed_update.py,sha256=6pq_wFMPwBxaeSHrLeQKvvMP4rDP1XYh7MQOHpBejYw,9922
test/test_chat_type.py,sha256=1t7WyhfgVzHXKCamAu39sgUNsCAqu79jnRoPbgIM9Ok,9806
test/test_chats_api.py,sha256=jdAn0O3kXB5g3qiNvDsEZejp2V7jhyFn27TJVHYc_Dc,11913
test/test_contact_attachment.py,sha256=W38se4Ywmv1W9E0nEdiT_EYax7V4swAj5DM8kD3e4Ac,9878
test/test_contact_attachment_payload.py,sha256=4aygeoy3iQtUI_JE-17glZYHOTxCRKQ-BRRxrrKlrRg,9936
test/test_contact_attachment_request.py,sha256=63Y3KWDIR-BqLgaIwL5DIkn8uEhTWsU2b5eaf1hMElU,9936
test/test_contact_attachment_request_payload.py,sha256=kw54nHWVh981MpYm_v114p38LgPdOZqB-aa4j6jsHhc,9994
test/test_data_attachment.py,sha256=dp02RuN737FIy0UkoDI27KGN7V2Gg19U0QH1NP9Dmy8,9854
test/test_emphasized_markup.py,sha256=IIvfqMQvZ4KqoPSRMQMfursCz2gUFKQVc66925cAj04,9870
test/test_error.py,sha256=sHsUVAfN_IxbE9342LPBe8pOEeRPlEX4Xax6en9H_fE,9780
test/test_file_attachment.py,sha256=pVj8R9Kt9956VYXVi9g3hx0U0NBpZrgwDYYE1rUnF3M,9854
test/test_file_attachment_payload.py,sha256=ZS05Z5zi_3CFRfdVnGscmdQMJNqqGelRG6lKHiaddB0,9912
test/test_file_attachment_request.py,sha256=d2_lcGCNQTTZ0yYEBmVHjuqIUxWM-17kkK7N61k2Prw,9912
test/test_get_pinned_message_result.py,sha256=rXIYgVFquTmnptRBrtqHBQP7e5NQswBV0VW2qyh5J1w,9922
test/test_get_subscriptions_result.py,sha256=KHhFu2mIPY5OwfD7AdWCvMCI34iiRRNvvCZwrEHcLRU,9920
test/test_heading_markup.py,sha256=9VCNwg0pX_HDZM5qi71RFGNB8Dh5TmRYRifBBMjPrqA,9846
test/test_highlighted_markup.py,sha256=ZcXwg5o5Qw5qNA8PuhIGFVicSFTILg33tPfsgwueumU,9878
test/test_image.py,sha256=TkK49MPz-aacXFpFPY3HuHYvYWpTUeKRcc5QuR5lzx0,9780
test/test_inline_keyboard_attachment.py,sha256=zgpA_QxxVTUSjtzaHtRUVosJq0LKYtgvlWb82GOnuAk,9936
test/test_inline_keyboard_attachment_request.py,sha256=BRMw-FonCwL0ON2pc0cKrE-Z0JzgJsS5GqK7lvb0icg,9994
test/test_inline_keyboard_attachment_request_payload.py,sha256=sanjSknuMujigSOysR-WpPmekozShy1qFXiQYJBXMDs,10052
test/test_intent.py,sha256=N97V9L62dddRSce93Mt_przPb7ktBlEGGd35f0GPknQ,9788
test/test_keyboard.py,sha256=Wz5i6ndFeglGimNwrSCl-d6AjtIsmyAycOfmpZcIufw,9804
test/test_link_button.py,sha256=Bcee_dt_TtTwPXMrMYMaXV8sjgx32vONfF5PE2hl0m4,9822
test/test_link_markup.py,sha256=ZvLOYg73E05eFYpFta9zg99-epqiVLydrSuQ6LwfhLQ,9822
test/test_linked_message.py,sha256=rcm_i0MTf1LVFDjHrnfWcuzi5Mr5Y66k0u3Z1IXAuks,9846
test/test_location_attachment.py,sha256=U2w6lFWtxQkHUhQVmXSxgRiaIr57fBXceCCeOLj8D6o,9886
test/test_location_attachment_request.py,sha256=KEy6hIoueVSwk5v1PmiqcpQwHbSRcHmjqkIo0MXJ2lY,9944
test/test_markup_element.py,sha256=quhMkSW87HYsfUCuL5rmIlBxv5ZbsyEEo6R7j0BXLek,9846
test/test_media_attachment_payload.py,sha256=UNl69VnhZp1mMv6Dbrc4UAlxSjiJ7rcMb0dyFUxIeao,9920
test/test_message.py,sha256=AS-sD0KTas2kqBVm_Q81I_-IxUrH_-2gMx3JBPJy76M,9796
test/test_message_body.py,sha256=BY64scsTfH1fTTn0esYi2WCt3-ApvwKi0o1Q0x5SF1M,9830
test/test_message_button.py,sha256=cgAw1gt9Ao2VozDdIL-E2M1wJH3X3JopnwgoSyKleYs,9846
test/test_message_callback_update.py,sha256=j40D8hYS4wmw5Wkt9udQ0m711nY2-N9dfFsvp7sRnZ8,9912
test/test_message_chat_created_update.py,sha256=Mp1QXJ2RKlmhLwRB365tKY-7TA5h3PqHa3ubokSR3ZA,9938
test/test_message_created_update.py,sha256=wDl03_z_ar9aJt-rWyCwangVtCEvfmvy9D1clkbloxo,9904
test/test_message_edited_update.py,sha256=IuMuxUcB_wOT5ui_D-t7Bqrq9FlswtYuV5O8kBx-p2M,9896
test/test_message_link_type.py,sha256=r5W2kf_MCj-QLObPe7Y_luM6MlU3ojuAB8AFX79L2SY,9864
test/test_message_list.py,sha256=iXQQH8SDlnCuy_uKdgcacHFbUO19WNkeQIR1asX-eYk,9830
test/test_message_removed_update.py,sha256=ZENBRQygtuXK7hka895t-4YdIXU_9Fo_tBqPCcwd_GM,9904
test/test_message_stat.py,sha256=yaadA-8ANh8CT6yPzezpIVC7Ay4kqQOevv4wKfI0LRo,9830
test/test_messages_api.py,sha256=SpbwTNkk-QiaZlRTgmZvT2DiTBIJwChpy-kzYkXPW-o,10657
test/test_monospaced_markup.py,sha256=IaUoq-Dbw18DkHcXT0xySZt89Fie3Nvqq9JE_k3yGbI,9870
test/test_new_message_body.py,sha256=b1j6yJWlJe1GTPayrZcxQLPZWI7tDmqsw-Go6PLomh8,9856
test/test_new_message_link.py,sha256=XXWH-73uCveBp5So1OILYV8OlAsMYBowaa2BRhmp8Ug,9856
test/test_photo_attachment.py,sha256=tCDudzv6N7NQxZa6ury0v8q9nAfHc385eH65BYowpZY,9862
test/test_photo_attachment_payload.py,sha256=Q8MAZq61dUwYCI35Adhb4t3_Laut1rgDTXazomPQ1eE,9920
test/test_photo_attachment_request.py,sha256=Wf5HST0RDUIPdmB38ZqmMHlXDP_tWmUFfpEdx98nzu4,9920
test/test_photo_attachment_request_payload.py,sha256=UUG7taPabvsEdCE04nBj4cR6ff64hJKWfrQO89iP2NY,9978
test/test_photo_token.py,sha256=0VGnVdEOsmWyWKYbpMdu32b_zcilusC-jNB965duLlg,9822
test/test_photo_tokens.py,sha256=mnlCMXoqs5sZWJX9v6hxIh3Y4S48qugYpfw8btnBeAU,9830
test/test_pin_message_body.py,sha256=IKo_ab01wRxXqJDxJxCWXlcagMd5gril9t63fi6CLyo,9856
test/test_recipient.py,sha256=Lb1CrjzAuSILI8mPwje0KZR51u1QNHVFJsWux48JtjE,9812
test/test_reply_button.py,sha256=Se_RUUcwvxS9VYDFW32fiOCsDfnAoOvvad-3-R-mRd8,9830
test/test_reply_keyboard_attachment.py,sha256=2OO5bluLmtf7xMJyyQCPBQzyhuuCoEka3IbTzW4hBbk,9928
test/test_reply_keyboard_attachment_request.py,sha256=xhjlafbFE5TF6cuoB20DFN94zSRdH0nY2jNsE-sFPbg,9986
test/test_request_contact_button.py,sha256=derwBTvgAOs-7bozWdZZoQ0hs0uM3OtEc9QWZnZxhO8,9904
test/test_request_geo_location_button.py,sha256=Bx6xOcEuvafnNrtu03mdXg005TPsSUl3N2TRScBd3r0,9938
test/test_send_contact_button.py,sha256=ohhiSoi9NAWHtYFUgMIedkgdmLChdR4VPq8fcxS1Rkw,9880
test/test_send_geo_location_button.py,sha256=hmF5BJMEjkdjnQftWwpkWKY_FqKPY8LD1znkWrj9NfU,9914
test/test_send_message_button.py,sha256=Jalb0GN7QjRBc4qWrI9LCdQt02TRseGLcxfz6fa7Jvs,9880
test/test_send_message_result.py,sha256=F85gHwH9C6L5hKy4fGgkBGRhiVhJ3-P46ni2KGmZDFw,9880
test/test_sender_action.py,sha256=UL9N5T1AgQ_Fxewm0lzpXgAl5K8x9J1JSnv0bmeBfBA,9838
test/test_share_attachment.py,sha256=bxj-q0W51_3wtj8ynBNK2St4m-ngQT-pBFF6HT21XHk,9862
test/test_share_attachment_payload.py,sha256=d_pjOKoGczbfH4015ySMSIKwGHgh3XjCPPkBWGIvtlk,9920
test/test_share_attachment_request.py,sha256=6ltjA_iqBie934fSr04RHjxUU1FWW0j8IAcKmn8sHck,9920
test/test_simple_query_result.py,sha256=thUATJPv7a-E_fHDZ2e-6WVMctHT2GlmRQxW_fN6abw,9880
test/test_sticker_attachment.py,sha256=xVGy8TEWOjZhhQiWDooG9YtJTGERObhBMmy8AZX-Ai4,9878
test/test_sticker_attachment_payload.py,sha256=FFaBYhUyr8a8k2p2UBMvMrPpv_PkIqfMufTXHmGvqL8,9936
test/test_sticker_attachment_request.py,sha256=rv7GxA1nj23YQQIwGujjg8dVTmjXS9juoE_4unvzkes,9936
test/test_sticker_attachment_request_payload.py,sha256=4sab9zsPbXz2_O9sXsbOYZrDcQ4Iv3bIaCk8MKlFc2s,9994
test/test_strikethrough_markup.py,sha256=Kbn_VuAr9H1QPQPNIlGP53_-b_wSYGpF5oAYhON4xWc,9894
test/test_strong_markup.py,sha256=ZmE0cniV97L9H_9W2R2UzD5jlCIyhWNn9p5SAWMKbM0,9838
test/test_subscription.py,sha256=BDs3TnHX2sZrvMamzk1M4gx1XZJbG4yRdT1hzRwkQRI,9836
test/test_subscription_request_body.py,sha256=31lCqX4O61xp5xvJp042Z4u9A8vFcniqhWZUiYVmdX8,9928
test/test_subscriptions_api.py,sha256=BudpLbYSdxwobpjRQ0j4Iptt1BqHD8VkIknQ2Aqq288,10218
test/test_text_format.py,sha256=wiDc_bI5P-Ir6qX--m7trICdoR9Lnpmz0NDPiijhJLA,9822
test/test_underline_markup.py,sha256=tCpgII91wtaFASV_c0ZsQgiMQVCUCx01OMdrMjaDegg,9862
test/test_update.py,sha256=3cr1RYCVPjNhvrEvwShojEUKt1LVfnytG2R41EdQi1I,9788
test/test_update_list.py,sha256=wwOABAuv0-L5Fwof5t4GDZUGAW8PXAD2zZf7p1Yrghk,9822
test/test_upload_api.py,sha256=uFen4VCbCtG9SfkKksfY5wmitVhcH9u7F5uI_ESjSK4,9783
test/test_upload_endpoint.py,sha256=0xuL0gc3AupMoq9TwMiSUoGRbmExVsOd70_uOeYdhdc,9854
test/test_upload_type.py,sha256=odVo_aOI7LgsT5ENE2-I2fAJesMADhCo2VQE3PAfZNA,9822
test/test_uploaded_info.py,sha256=IYzUhqtKH6nhpjb2IM0vNSV6OyMIo-eqErMu1MimZ7A,9838
test/test_user.py,sha256=p-5inkNe10KYwgp5YLBdg3oToUcow6T03oaDlFWyyHw,9772
test/test_user_added_to_chat_update.py,sha256=f58Klq59jQ_apn0zA3MxkJPzai4P_orbBQfVy4J2cKo,9916
test/test_user_ids_list.py,sha256=wAjg1iDFvfdfD4LwkvSW54c3TlkZQT9hLlwifi0PDAY,9832
test/test_user_mention_markup.py,sha256=_P5e5ocWxvEvSBymlNGfzH8uYzmcpMXgjpClA_lbF50,9880
test/test_user_removed_from_chat_update.py,sha256=RfFzgoinHgpbRLvakW92NzKGuRnlwWITZvDZpFCPVsM,9948
test/test_user_with_photo.py,sha256=49CS0DLDijT0H2B5MxC1TTYqKrGl72KxaqmYBE4_ZNA,9848
test/test_video_attachment.py,sha256=pFxpXgf4ZQNjpkKPfFvtoBhwnkDxB09iXQdvO3wxUTI,9862
test/test_video_attachment_details.py,sha256=Wmk_B-TIs3JwG0hw69hzXDyPQA-rcDfceHX_sKdeJdM,9920
test/test_video_attachment_request.py,sha256=f5pi78wufxa6nJ4o1Ad79qlb1LKkSFpTFxtcnXrG-u0,9920
test/test_video_thumbnail.py,sha256=n6f0PFZBBh1k0fqIUThSUaSxFIaNFfxE6nLu8lD1g6g,9854
test/test_video_urls.py,sha256=URELhZjwJNAXjB1TmVvqa64NyBwlQNICbjzPEBCTuB8,9814
