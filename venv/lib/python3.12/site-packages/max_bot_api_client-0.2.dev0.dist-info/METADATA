Metadata-Version: 2.4
Name: max-bot-api-client
Version: 0.2.dev0
Summary: Max Bot API
Home-page: https://github.com/max-messenger/max-bot-api-client-py
Author-email: d<PERSON><PERSON><PERSON><PERSON>@juicedev.org
License: GNU LGPL v3.0
Keywords: Max Bot API
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Programming Language :: Python
License-File: LICENSE
Requires-Dist: urllib3>=1.15
Requires-Dist: six>=1.10
Requires-Dist: certifi
Requires-Dist: python-dateutil
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

    # About Bot API allows bots to interact with Max. Methods are called by sending HTTPS requests to [botapi.max.ru](https://botapi.max.ru) domain. Bots are third-party applications that use Max features. A bot can legitimately take part in a conversation. It can be achieved through HTTP requests to the Max Bot API.  ## Features Max bots of the current version are able to: - Communicate with users and respond to requests - Recommend users complete actions via programmed buttons - Request personal data from users (name, short reference, phone number) We&#39;ll keep working on expanding bot capabilities in the future.  
    
