#!/usr/bin/env python3
"""
Скрипт для настройки webhook в Max Bot API
"""

import os
import sys
from dotenv import load_dotenv
import max_client
from max_client.rest import ApiException

# Загрузка переменных окружения
load_dotenv()

MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
WEBHOOK_URL = "https://closing-pup-flying.ngrok-free.app/webhook/max"

def setup_webhook():
    """Настройка webhook для бота"""
    try:
        # Инициализация API клиента
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN
        
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)
        
        print("🔧 Настройка webhook...")
        print(f"📡 URL: {WEBHOOK_URL}")
        
        # Сначала получаем текущие подписки
        try:
            current_subscriptions = subscriptions_api.get_subscriptions()
            print(f"📋 Текущие подписки: {current_subscriptions}")
            
            # Если есть подписки, удаляем их
            if hasattr(current_subscriptions, 'subscriptions') and current_subscriptions.subscriptions:
                for subscription in current_subscriptions.subscriptions:
                    try:
                        subscriptions_api.unsubscribe(subscription.url)
                        print(f"🗑️ Удалена подписка: {subscription.url}")
                    except ApiException as e:
                        print(f"⚠️ Ошибка удаления подписки: {e}")
                        
        except ApiException as e:
            print(f"📋 Не удалось получить подписки: {e}")
        
        # Создаем новую подписку
        subscription_body = max_client.SubscriptionRequestBody(
            url=WEBHOOK_URL,
            update_types=['message_created', 'message_callback'],
            version='0.1.0'
        )
        
        # Устанавливаем webhook
        result = subscriptions_api.subscribe(subscription_body)
        
        print("✅ Webhook успешно настроен!")
        print(f"📊 Результат: {result}")
        
        # Проверяем установку
        subscriptions = subscriptions_api.get_subscriptions()
        print(f"🔍 Проверка подписок: {subscriptions}")
        
        return True
        
    except ApiException as e:
        print(f"❌ Ошибка API: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def remove_webhook():
    """Удаление webhook"""
    try:
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN
        
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)
        
        print("🗑️ Удаление webhook...")
        
        # Получаем текущие подписки
        subscriptions = subscriptions_api.get_subscriptions()
        
        if hasattr(subscriptions, 'subscriptions') and subscriptions.subscriptions:
            for subscription in subscriptions.subscriptions:
                subscriptions_api.unsubscribe(subscription.url)
                print(f"✅ Удалена подписка: {subscription.url}")
        else:
            print("📭 Активных подписок не найдено")
            
        return True
        
    except ApiException as e:
        print(f"❌ Ошибка API: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def check_webhook():
    """Проверка текущих webhook подписок"""
    try:
        configuration = max_client.Configuration()
        configuration.api_key['access_token'] = MAX_BOT_TOKEN
        
        api_client = max_client.ApiClient(configuration)
        subscriptions_api = max_client.SubscriptionsApi(api_client)
        
        print("🔍 Проверка webhook подписок...")
        
        subscriptions = subscriptions_api.get_subscriptions()
        
        if hasattr(subscriptions, 'subscriptions') and subscriptions.subscriptions:
            print(f"📊 Найдено подписок: {len(subscriptions.subscriptions)}")
            for i, subscription in enumerate(subscriptions.subscriptions, 1):
                print(f"  {i}. URL: {subscription.url}")
                print(f"     Типы: {subscription.update_types}")
                print(f"     Время: {subscription.time}")
        else:
            print("📭 Активных подписок не найдено")
            
        return True
        
    except ApiException as e:
        print(f"❌ Ошибка API: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def main():
    """Главная функция"""
    if not MAX_BOT_TOKEN:
        print("❌ Не найден MAX_BOT_TOKEN в .env файле")
        sys.exit(1)
    
    if len(sys.argv) < 2:
        print("📖 Использование:")
        print("  python setup_webhook.py setup    - Установить webhook")
        print("  python setup_webhook.py remove   - Удалить webhook")
        print("  python setup_webhook.py check    - Проверить webhook")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'setup':
        success = setup_webhook()
    elif command == 'remove':
        success = remove_webhook()
    elif command == 'check':
        success = check_webhook()
    else:
        print(f"❌ Неизвестная команда: {command}")
        sys.exit(1)
    
    if success:
        print("🎉 Операция выполнена успешно!")
    else:
        print("💥 Операция завершилась с ошибкой!")
        sys.exit(1)

if __name__ == "__main__":
    main()
