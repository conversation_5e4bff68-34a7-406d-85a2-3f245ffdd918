import os
import max_client
from max_client.rest import ApiException
from dotenv import load_dotenv

load_dotenv()

# Configure API key authorization: access_token
configuration = max_client.Configuration()
configuration.api_key['access_token'] = os.getenv('MAX_BOT_TOKEN')

# create an instance of the API class
api_instance = max_client.BotsApi(max_client.ApiClient(configuration))

try:
    # Get current bot info
    api_response = api_instance.get_my_info()
    print("Bot Info:")
    print(f"  ID: {api_response.id}")
    print(f"  Name: {api_response.name}")
    print(f"  Is Bot: {bool(api_response.is_bot)}")
    if api_response.avatar:
        print(f"  Avatar: {api_response.avatar}")
except ApiException as e:
    print(f"Exception when calling BotsApi->get_my_info: {e}")
except ValueError as e:
    print(f"A ValueError occurred, likely due to 'is_bot' field: {e}")
    # Attempt to extract and print bot info from the exception arguments
    if e.args and isinstance(e.args[0], dict):
        bot_info_dict = e.args[0]
        print("Bot Info (extracted from error):")
        if 'user_id' in bot_info_dict:
            print(f"  ID: {bot_info_dict['user_id']}")
        if 'name' in bot_info_dict:
            print(f"  Name: {bot_info_dict['name']}")
        if 'is_bot' in bot_info_dict:
            print(f"  Is Bot: {bool(bot_info_dict['is_bot'])}")
        if 'avatar' in bot_info_dict:
            print(f"  Avatar: {bot_info_dict['avatar']}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")