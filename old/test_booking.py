#!/usr/bin/env python3
"""
Скрипт для тестирования записи в базу данных
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_NAME = os.getenv('DB_NAME', 'max_ai_bot')
DB_USER = os.getenv('DB_USER', 'postgres')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
DB_PORT = os.getenv('DB_PORT', '5432')

def test_booking():
    """Тестирует функцию записи"""
    try:
        print("🔍 Тестирование записи в базу данных...")
        
        conn = psycopg2.connect(host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PASSWORD, port=DB_PORT)
        cur = conn.cursor()
        
        # Показываем доступные записи
        cur.execute("SELECT id, service_name, appointment_date, appointment_time, is_booked FROM appointments WHERE is_booked = FALSE")
        available = cur.fetchall()
        
        print(f"\n📋 Доступные записи ({len(available)}):")
        for appt in available:
            print(f"  ID: {appt[0]} - {appt[1]} на {appt[2]} в {appt[3]}")
        
        if not available:
            print("❌ Нет доступных записей для тестирования")
            return
            
        # Берем первую доступную запись
        test_appointment_id = available[0][0]
        test_user_id = "test_user_123"
        test_user_name = "Тестовый Пользователь"
        
        print(f"\n🎯 Тестируем запись на appointment ID: {test_appointment_id}")
        
        # Проверяем что запись существует и свободна
        cur.execute(
            "SELECT id, service_name, appointment_date, appointment_time, is_booked FROM appointments WHERE id = %s",
            (test_appointment_id,)
        )
        existing_appt = cur.fetchone()
        
        if not existing_appt:
            print(f"❌ Запись {test_appointment_id} не найдена")
            return
            
        if existing_appt[4]:  # is_booked
            print(f"❌ Запись {test_appointment_id} уже забронирована")
            return
            
        print(f"✅ Запись найдена: {existing_appt}")
        
        # Выполняем бронирование
        cur.execute(
            """
            UPDATE appointments
            SET is_booked = TRUE, booked_by_user_id = %s, booked_by_user_name = %s
            WHERE id = %s AND is_booked = FALSE RETURNING service_name, appointment_date, appointment_time;
            """,
            (test_user_id, test_user_name, test_appointment_id)
        )
        
        booked_appt = cur.fetchone()
        
        if booked_appt:
            conn.commit()
            print(f"✅ УСПЕХ! Запись забронирована:")
            print(f"   Услуга: {booked_appt[0]}")
            print(f"   Дата: {booked_appt[1]}")
            print(f"   Время: {booked_appt[2]}")
            print(f"   Пользователь: {test_user_name} (ID: {test_user_id})")
        else:
            print(f"❌ ОШИБКА: UPDATE не вернул результат")
            conn.rollback()
            
        # Проверяем результат
        cur.execute(
            "SELECT is_booked, booked_by_user_id, booked_by_user_name FROM appointments WHERE id = %s",
            (test_appointment_id,)
        )
        check_result = cur.fetchone()
        print(f"\n🔍 Проверка результата:")
        print(f"   Забронировано: {check_result[0]}")
        print(f"   ID пользователя: {check_result[1]}")
        print(f"   Имя пользователя: {check_result[2]}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")

if __name__ == "__main__":
    test_booking()
