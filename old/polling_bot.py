import os
import time
import json
import max_client
from max_client.rest import ApiException
from dotenv import load_dotenv
from max_client.models.message_created_update import MessageCreatedUpdate
from max_client.models.callback_button import CallbackButton
from max_client.models.inline_keyboard_attachment_request import InlineKeyboardAttachmentRequest
from max_client.models.inline_keyboard_attachment_request_payload import InlineKeyboardAttachmentRequestPayload

load_dotenv()

# Configure API key authorization: access_token
configuration = max_client.Configuration()
configuration.api_key['access_token'] = os.getenv('MAX_BOT_TOKEN')

# Create API instances
subscriptions_api = max_client.SubscriptionsApi(max_client.ApiClient(configuration))
messages_api = max_client.MessagesApi(max_client.ApiClient(configuration))

appointments = []
try:
    with open('/home/<USER>/My/max-ai/appointments.json', 'r', encoding='utf-8') as f:
        appointments = json.load(f)
except FileNotFoundError:
    print("Error: appointments.json not found. Please create the file.")
except json.JSONDecodeError:
    print("Error: Could not decode appointments.json. Check file format.")

print("Starting polling bot...")

marker = 0 # Use 0 for the first page to get all uncommitted updates

while True:
    try:
        print(f"Polling for updates with marker: {marker}")
        # Get updates using long polling
        api_response = subscriptions_api.get_updates(timeout=30, marker=marker)
        
        if api_response.updates:
            print(f"Received {len(api_response.updates)} updates.")
            for update in api_response.updates:
                # Process only message_created updates for simplicity
                if isinstance(update, MessageCreatedUpdate):
                    message = update.message
                    # Check if message and message.text exist before accessing
                    if message and hasattr(message.body, 'text') and message.body.text and message.sender:
                        user_name = message.sender.name or message.sender.first_name
                        print(f"Message from {user_name} (ID: {message.sender.user_id}): {message.body.text}")
                        
                        print(f"Detected message. Preparing to send reply to {user_name}.")
                        
                        appointments_text = ""
                        buttons = []
                        if appointments:
                            appointments_text = "\n\nДоступные даты и время для записи:\n"
                            for appt in appointments:
                                button_text = f"{appt['date']} в {appt['time']}"
                                appointments_text += f"- {button_text}\n"
                                # Используем CallbackButton для встраиваемых кнопок
                                buttons.append(max_client.CallbackButton(text=button_text, payload=button_text))
                        else:
                            appointments_text = "\n\nК сожалению, пока нет доступных занятий."

                        print(f"DEBUG: appointments: {appointments}")
                        print(f"DEBUG: buttons: {buttons}")

                        message_attachments = []
                        if buttons:
                            # Группируем кнопки в строки (по одной кнопке на строку для простоты)
                            keyboard_rows = [[button] for button in buttons]
                            print(f"DEBUG: keyboard_rows: {keyboard_rows}")
                            
                            # Создаем payload для встраиваемой клавиатуры
                            inline_keyboard_payload = max_client.InlineKeyboardAttachmentRequestPayload(buttons=keyboard_rows)
                            
                            # Создаем запрос на вложение встраиваемой клавиатуры
                            inline_keyboard_attachment = max_client.InlineKeyboardAttachmentRequest(payload=inline_keyboard_payload)
                            
                            message_attachments.append(inline_keyboard_attachment)

                        new_message_body = max_client.NewMessageBody(
                            text=f"Привет, {user_name}!{appointments_text}Я могу помочь вам записаться на урок. Выберите удобное время:",
                            attachments=message_attachments
                        )
                        
                        print(f"Attempting to send message...")
                        try:
                            # Send message back to the user
                            messages_api.send_message(new_message_body, user_id=message.sender.user_id)
                            print(f"Successfully sent greeting to {user_name}")
                        except ApiException as send_e:
                            print(f"Error sending message to {user_name}: {send_e}")
                            

            # Update marker for the next poll
            marker = api_response.marker
            print(f"Updated marker to: {marker}")

    except ApiException as e:
        print(f"Exception when calling SubscriptionsApi->get_updates: {e}")
        time.sleep(5) # Wait before retrying on API error
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        time.sleep(5) # Wait before retrying on other errors
