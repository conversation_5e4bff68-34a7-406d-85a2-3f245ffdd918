import os
import time
import json
import max_client
from max_client.rest import ApiException
from dotenv import load_dotenv
from max_client.models.message_created_update import MessageCreatedUpdate

import openai

load_dotenv()

# Configure API key authorization for Max Bot API
configuration = max_client.Configuration()
configuration.api_key['access_token'] = os.getenv('MAX_BOT_TOKEN')

# Configure OpenAI API
openai.api_key = os.getenv('OPENAI_API_KEY')
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')

print(f"DEBUG: OPENAI_API_KEY loaded: {bool(openai.api_key)}")
print(f"DEBUG: OPENAI_MODEL loaded: {OPENAI_MODEL}")

# Create API instances
subscriptions_api = max_client.SubscriptionsApi(max_client.ApiClient(configuration))
messages_api = max_client.MessagesApi(max_client.ApiClient(configuration))

appointments = []
try:
    with open('/home/<USER>/My/max-ai/appointments.json', 'r', encoding='utf-8') as f:
        appointments = json.load(f)
except FileNotFoundError:
    print("Error: appointments.json not found. Please create the file.")
except json.JSONDecodeError:
    print("Error: Could not decode appointments.json. Check file format.")

print("Starting OpenAI polling bot...")

marker = 0 # Use 0 for the first page to get all uncommitted updates

while True:
    try:
        print(f"Polling for updates with marker: {marker}")
        # Get updates using long polling
        api_response = subscriptions_api.get_updates(timeout=30, marker=marker)
        
        if api_response.updates:
            print(f"Received {len(api_response.updates)} updates.")
            for update in api_response.updates:
                # Process only message_created updates for simplicity
                if isinstance(update, MessageCreatedUpdate):
                    message = update.message
                    # Check if message and message.text exist before accessing
                    if message and hasattr(message.body, 'text') and message.body.text and message.sender:
                        user_name = message.sender.name or message.sender.first_name
                        user_id = message.sender.user_id
                        user_message_text = message.body.text
                        print(f"Message from {user_name} (ID: {user_id}): {user_message_text}")
                        
                        # Prepare available appointments for OpenAI
                        available_appointments_str = ""
                        if appointments:
                            available_appointments_str = "\n\nДоступные даты и время для записи:\n"
                            for appt in appointments:
                                available_appointments_str += f"- {appt['date']} в {appt['time']}\n"
                        else:
                            available_appointments_str = "\n\nК сожалению, пока нет доступных занятий."

                        # Construct prompt for OpenAI
                        prompt = f"""Ты - бот-репетитор. Твоя задача - приветствовать пользователя и предложить ему записаться на занятие. 
У тебя есть следующие доступные даты и время для записи:{available_appointments_str}

Пользователь: {user_message_text}

Твой ответ (приветствие и предложение записи):"""

                        print(f"Attempting to get response from OpenAI...")
                        try:
                            response = openai.chat.completions.create(
                                model=OPENAI_MODEL,
                                messages=[
                                    {"role": "system", "content": "Ты - дружелюбный бот-репетитор, который помогает записываться на занятия."},
                                    {"role": "user", "content": prompt}
                                ],
                                max_tokens=200
                            )
                            ai_response_text = response.choices[0].message.content
                            
                            # Log token usage
                            prompt_tokens = response.usage.prompt_tokens
                            completion_tokens = response.usage.completion_tokens
                            total_tokens = response.usage.total_tokens
                            print(f"OpenAI Token Usage: Prompt: {prompt_tokens}, Completion: {completion_tokens}, Total: {total_tokens}")

                            print(f"AI Response: {ai_response_text}")
                            
                            new_message_body = max_client.NewMessageBody(text=ai_response_text)
                            
                            print(f"Attempting to send message to {user_name}...")
                            messages_api.send_message(new_message_body, user_id=user_id)
                            print(f"Successfully sent AI response to {user_name}")

                        except openai.APIError as e:
                            print(f"OpenAI API Error: {e}")
                            error_message = "Извините, произошла ошибка при обращении к AI. Пожалуйста, попробуйте позже."
                            messages_api.send_message(max_client.NewMessageBody(text=error_message), user_id=user_id)
                        except Exception as e:
                            print(f"An unexpected error occurred with OpenAI: {e}")
                            error_message = "Извините, произошла непредвиденная ошибка. Пожалуйста, попробуйте позже."
                            messages_api.send_message(max_client.NewMessageBody(text=error_message), user_id=user_id)
                            

            # Update marker for the next poll
            marker = api_response.marker
            print(f"Updated marker to: {marker}")

    except ApiException as e:
        print(f"Exception when calling SubscriptionsApi->get_updates: {e}")
        time.sleep(5) # Wait before retrying on API error
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        time.sleep(5) # Wait before retrying on other errors
