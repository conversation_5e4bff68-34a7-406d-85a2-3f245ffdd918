#!/usr/bin/env python3
"""
Скрипт для получения текущего ngrok URL и автоматической настройки webhook
"""

import requests
import json
import sys

def get_ngrok_url():
    """Получить текущий ngrok URL"""
    try:
        # Запрашиваем API ngrok (локальный)
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            if tunnels:
                # Ищем HTTPS туннель
                for tunnel in tunnels:
                    if tunnel.get('proto') == 'https':
                        public_url = tunnel.get('public_url')
                        config = tunnel.get('config', {})
                        local_addr = config.get('addr', 'неизвестно')
                        
                        print(f"🌐 Найден ngrok туннель:")
                        print(f"   📡 Публичный URL: {public_url}")
                        print(f"   🏠 Локальный адрес: {local_addr}")
                        
                        return public_url
                
                print("⚠️ HTTPS туннель не найден")
                print("📋 Доступные туннели:")
                for tunnel in tunnels:
                    print(f"   - {tunnel.get('proto')}: {tunnel.get('public_url')}")
                return None
            else:
                print("📭 Активных туннелей не найдено")
                return None
        else:
            print(f"❌ Ошибка запроса к ngrok API: {response.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Не удалось подключиться к ngrok API")
        print("💡 Убедитесь, что ngrok запущен")
        return None
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None

def auto_setup_webhook(ngrok_url):
    """Автоматически настроить webhook с ngrok URL"""
    import subprocess
    
    try:
        print(f"\n🚀 Автоматическая настройка webhook...")
        
        # Запускаем скрипт установки URL
        result = subprocess.run([
            './venv/bin/python', 
            'set_webhook_url.py', 
            ngrok_url
        ], capture_output=True, text=True, input='y\n')
        
        if result.returncode == 0:
            print("✅ Webhook успешно настроен!")
            return True
        else:
            print(f"❌ Ошибка настройки: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка автонастройки: {e}")
        return False

def main():
    """Главная функция"""
    
    print("🔍 Поиск ngrok туннеля...")
    
    ngrok_url = get_ngrok_url()
    
    if ngrok_url:
        print(f"\n✅ Ngrok URL найден: {ngrok_url}")
        
        if len(sys.argv) > 1 and sys.argv[1] == 'auto':
            # Автоматическая настройка
            auto_setup_webhook(ngrok_url)
        else:
            # Ручная настройка
            print(f"\n🔧 Для настройки webhook выполните:")
            print(f"   python set_webhook_url.py {ngrok_url}")
            print(f"\n🚀 Или автоматически:")
            print(f"   python get_ngrok_url.py auto")
    else:
        print("\n💡 Инструкции по запуску ngrok:")
        print("   1. Запустите ngrok: ngrok http 8000")
        print("   2. Скопируйте HTTPS URL")
        print("   3. Выполните: python set_webhook_url.py <URL>")

if __name__ == "__main__":
    main()
