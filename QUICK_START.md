# 🚀 Быстрый старт - Смена домена для webhook

## 📋 Что нужно сделать:

### 1️⃣ Перенастроить ngrok на порт 8000

```bash
# Остановить текущий ngrok (Ctrl+C)
# Запустить на порту 8000
ngrok http 8000
```

### 2️⃣ Автоматически настроить webhook

```bash
# Найти новый ngrok URL и настроить webhook
./venv/bin/python get_ngrok_url.py auto
```

### 3️⃣ Или вручную

```bash
# 1. Скопировать новый HTTPS URL из ngrok
# 2. Установить новый URL
./venv/bin/python set_webhook_url.py https://новый-домен.ngrok-free.app
```

## 🔧 Полезные команды:

```bash
# Показать текущий webhook URL
./venv/bin/python set_webhook_url.py show

# Найти ngrok URL
./venv/bin/python get_ngrok_url.py

# Установить новый домен
./venv/bin/python set_webhook_url.py https://example.com

# Проверить webhook в Max API
./venv/bin/python setup_webhook.py check

# Запустить сервер на нужном порту
./venv/bin/python main.py 8000
```

## 🎯 Пример полного процесса:

```bash
# 1. Запустить ngrok на 8000
ngrok http 8000

# 2. В новом терминале - автонастройка
./venv/bin/python get_ngrok_url.py auto

# 3. Запустить бота
./venv/bin/python main.py 8000
```

## ✅ Проверка работы:

```bash
# Health check
curl http://localhost:8000/health

# Через ngrok (замените на ваш URL)
curl https://ваш-домен.ngrok-free.app/health
```

## 🔄 Смена домена в любое время:

Теперь можно легко менять домен без редактирования кода:

```bash
# Новый временный домен от ngrok
./venv/bin/python set_webhook_url.py https://new-123.ngrok-free.app

# Постоянный домен
./venv/bin/python set_webhook_url.py https://mybot.example.com

# Локальный тест (через ngrok)
./venv/bin/python get_ngrok_url.py auto
```

## 🎉 Готово!

После настройки бот будет получать сообщения мгновенно через webhook!
