#!/usr/bin/env python3
"""
Модуль для работы с базой данных
"""

import os
import asyncpg
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

# Конфигурация БД
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_NAME = os.getenv('DB_NAME')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = int(os.getenv('DB_PORT', 5432))

# Глобальный пул соединений
db_pool = None

async def init_db():
    """Инициализация базы данных"""
    global db_pool
    
    db_pool = await asyncpg.create_pool(
        host=DB_HOST,
        port=DB_PORT,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME,
        min_size=1,
        max_size=10
    )
    
    # Создаем таблицу если не существует
    async with db_pool.acquire() as conn:
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS appointments (
                id SERIAL PRIMARY KEY,
                service_name VARCHAR(255) NOT NULL,
                appointment_date DATE NOT NULL,
                appointment_time TIME NOT NULL,
                is_booked BOOLEAN DEFAULT FALSE,
                booked_by_user_id TEXT,
                booked_by_user_name TEXT
            );
        """)
    
    logger.info("База данных инициализирована")

async def close_db():
    """Закрытие соединений с БД"""
    global db_pool
    if db_pool:
        await db_pool.close()
        logger.info("Соединения с БД закрыты")

async def clear_appointments():
    """Очистить все записи"""
    try:
        async with db_pool.acquire() as conn:
            count = await conn.fetchval("SELECT COUNT(*) FROM appointments")
            await conn.execute("DELETE FROM appointments")
            logger.info(f"Удалено {count} записей из БД")
            return count
    except Exception as e:
        logger.error(f"Ошибка очистки БД: {e}")
        return 0

async def add_appointment_to_db(service_name: str, appt_date, appt_time):
    """Добавить запись в БД (для админа)"""
    try:
        # Конвертируем строки в объекты date/time если нужно
        if isinstance(appt_date, str):
            from datetime import datetime
            appt_date = datetime.strptime(appt_date, '%Y-%m-%d').date()
        if isinstance(appt_time, str):
            from datetime import datetime
            appt_time = datetime.strptime(appt_time, '%H:%M:%S').time()

        async with db_pool.acquire() as conn:
            result = await conn.fetchval("""
                INSERT INTO appointments (service_name, appointment_date, appointment_time, is_booked)
                VALUES ($1, $2, $3, FALSE)
                RETURNING id
            """, service_name, appt_date, appt_time)
            logger.info(f"Добавлена запись ID {result}: {service_name} на {appt_date} в {appt_time}")
            return result
    except Exception as e:
        logger.error(f"Ошибка добавления записи: {e}")
        return None

async def load_appointments_from_db():
    """Загрузить все записи из БД (для админа)"""
    try:
        async with db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT id, service_name, appointment_date, appointment_time, is_booked, booked_by_user_name
                FROM appointments
                ORDER BY appointment_date, appointment_time
            """)
            
            appointments = []
            for row in rows:
                appointments.append({
                    'id': row['id'],
                    'service_name': row['service_name'],
                    'date': row['appointment_date'].strftime('%Y-%m-%d'),
                    'time': row['appointment_time'].strftime('%H:%M:%S'),
                    'is_booked': row['is_booked'],
                    'booked_by': row['booked_by_user_name']
                })
            return appointments
    except Exception as e:
        logger.error(f"Ошибка загрузки записей: {e}")
        return []

async def get_available_appointments():
    """Получить доступные записи для пользователей"""
    try:
        async with db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT id, service_name, appointment_date, appointment_time
                FROM appointments
                WHERE is_booked = FALSE
                ORDER BY appointment_date, appointment_time
                LIMIT 10
            """)
            
            appointments = []
            for row in rows:
                appointments.append({
                    'id': row['id'],
                    'service_name': row['service_name'],
                    'date': row['appointment_date'].strftime('%d.%m'),
                    'time': row['appointment_time'].strftime('%H:%M')
                })
            return appointments
    except Exception as e:
        logger.error(f"Ошибка получения доступных записей: {e}")
        return []

async def book_appointment(appointment_id: int, user_id: str, user_name: str):
    """Забронировать запись"""
    try:
        async with db_pool.acquire() as conn:
            # Проверяем что запись свободна и бронируем
            result = await conn.fetchrow("""
                UPDATE appointments 
                SET is_booked = TRUE, booked_by_user_id = $1, booked_by_user_name = $2
                WHERE id = $3 AND is_booked = FALSE
                RETURNING service_name, appointment_date, appointment_time
            """, user_id, user_name, appointment_id)
            
            if result:
                booked_info = {
                    'service': result['service_name'],
                    'date': result['appointment_date'].strftime('%Y-%m-%d'),
                    'time': result['appointment_time'].strftime('%H:%M')
                }
                logger.info(f"Забронирована запись ID {appointment_id} пользователем {user_name} (ID: {user_id})")
                return booked_info
            return None
    except Exception as e:
        logger.error(f"Ошибка бронирования: {e}")
        return None

async def get_user_appointments(user_id: str):
    """Получить записи пользователя"""
    try:
        async with db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT id, service_name, appointment_date, appointment_time
                FROM appointments
                WHERE booked_by_user_id = $1 AND is_booked = TRUE
                ORDER BY appointment_date, appointment_time
            """, user_id)
            
            appointments = []
            for row in rows:
                appointments.append({
                    'id': row['id'],
                    'service_name': row['service_name'],
                    'date': row['appointment_date'].strftime('%d.%m.%Y'),
                    'time': row['appointment_time'].strftime('%H:%M')
                })
            return appointments
    except Exception as e:
        logger.error(f"Ошибка получения записей пользователя: {e}")
        return []

async def delete_appointment(appointment_id: int):
    """Удалить запись (для админа)"""
    try:
        async with db_pool.acquire() as conn:
            result = await conn.fetchrow("""
                DELETE FROM appointments 
                WHERE id = $1
                RETURNING service_name, appointment_date, appointment_time
            """, appointment_id)
            
            if result:
                logger.info(f"Удалена запись ID {appointment_id}: {result['service_name']}")
                return True
            return False
    except Exception as e:
        logger.error(f"Ошибка удаления записи: {e}")
        return False
