#!/usr/bin/env python3
"""
Обработчики сообщений бота
"""

import os
import logging
from dotenv import load_dotenv
import max_client
from max_client.models.callback_button import CallbackButton
from max_client.models.inline_keyboard_attachment_request import InlineKeyboardAttachmentRequest
from max_client.models.inline_keyboard_attachment_request_payload import InlineKeyboardAttachmentRequestPayload
from database import (
    add_appointment_to_db, 
    load_appointments_from_db, 
    get_available_appointments, 
    book_appointment,
    get_user_appointments,
    delete_appointment
)

load_dotenv()

logger = logging.getLogger(__name__)

# Конфигурация
MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
ADMIN_ID = os.getenv('ADMIN_ID')

# Max API клиент
configuration = max_client.Configuration()
configuration.api_key['access_token'] = MAX_BOT_TOKEN
messages_api = max_client.MessagesApi(max_client.ApiClient(configuration))

def send_message_to_user(text: str, user_id: int):
    """Отправить сообщение пользователю"""
    try:
        message_body = max_client.NewMessageBody(text=text)
        messages_api.send_message(message_body, user_id=user_id)
        logger.info(f"Сообщение отправлено пользователю {user_id}")
    except Exception as e:
        logger.error(f"Ошибка отправки сообщения: {e}")

def send_appointments_keyboard(text: str, user_id: int, appointments: list):
    """Отправить клавиатуру с записями"""
    try:
        if not appointments:
            send_message_to_user("😔 К сожалению, свободных записей нет.", user_id)
            return
        
        # Создаем кнопки
        buttons = []
        for apt in appointments:
            button_text = f"{apt['service_name']} - {apt['date']} {apt['time']}"
            button = CallbackButton(text=button_text, payload=f"book_{apt['id']}")
            buttons.append([button])
        
        # Создаем клавиатуру
        keyboard_payload = InlineKeyboardAttachmentRequestPayload(buttons=buttons)
        keyboard_attachment = InlineKeyboardAttachmentRequest(payload=keyboard_payload)
        
        # Отправляем сообщение
        message_body = max_client.NewMessageBody(
            text=text,
            attachments=[keyboard_attachment]
        )
        messages_api.send_message(message_body, user_id=user_id)
        logger.info(f"Клавиатура отправлена пользователю {user_id}")
        
    except Exception as e:
        logger.error(f"Ошибка отправки клавиатуры: {e}")

async def handle_message(user_id: int, user_name: str, message_text: str):
    """Обработка текстового сообщения"""
    logger.info(f"Сообщение от {user_name} (ID: {user_id}): {message_text}")
    
    # Очищаем текст от эмодзи в начале
    clean_text = message_text
    if message_text.startswith('👋'):
        clean_text = message_text[1:]
    
    # Админские команды
    if str(user_id) == ADMIN_ID:
        if clean_text.startswith("/add_appointment"):
            parts = clean_text.split(" ", 3)
            if len(parts) == 4:
                service_name = parts[1]
                appt_date = parts[2]
                appt_time = parts[3]
                appt_id = await add_appointment_to_db(service_name, appt_date, appt_time)
                if appt_id:
                    send_message_to_user(f"✅ Запись '{service_name}' на {appt_date} в {appt_time} добавлена (ID: {appt_id}).", user_id)
                else:
                    send_message_to_user("❌ Ошибка при добавлении записи. Возможно, такая запись уже существует.", user_id)
            else:
                send_message_to_user("📖 Использование: /add_appointment <услуга> <дата YYYY-MM-DD> <время HH:MM:SS>", user_id)
        
        elif clean_text == "/list_appointments":
            all_appointments = await load_appointments_from_db()
            if all_appointments:
                response_text = "📋 Все записи:\n\n"
                for appt in all_appointments:
                    status = "🔴 Забронировано" if appt['is_booked'] else "🟢 Свободно"
                    booked_by = f" - {appt['booked_by']}" if appt['is_booked'] and appt['booked_by'] else ""
                    response_text += f"ID {appt['id']}: {appt['service_name']} {appt['date']} в {appt['time']} {status}{booked_by}\n"
            else:
                response_text = "📭 Нет записей в базе данных."
            send_message_to_user(response_text, user_id)
        
        elif clean_text.startswith("/delete_appointment"):
            parts = clean_text.split(" ", 1)
            if len(parts) == 2:
                try:
                    appointment_id = int(parts[1])
                    if await delete_appointment(appointment_id):
                        send_message_to_user(f"✅ Запись ID {appointment_id} удалена.", user_id)
                    else:
                        send_message_to_user(f"❌ Запись ID {appointment_id} не найдена.", user_id)
                except ValueError:
                    send_message_to_user("❌ Неверный ID записи. Используйте: /delete_appointment <ID>", user_id)
            else:
                send_message_to_user("📖 Использование: /delete_appointment <ID>", user_id)
        
        else:
            send_message_to_user(f"👋 Привет, {user_name}! Я бот для записи.\n\n📋 Команды админа:\n/add_appointment <услуга> <дата> <время>\n/list_appointments\n/delete_appointment <ID>", user_id)
    
    else:  # Обычные пользователи
        # Проверяем есть ли у пользователя записи
        user_appointments = await get_user_appointments(str(user_id))
        
        if user_appointments:
            # Показываем существующие записи + кнопку "Записаться еще"
            response_text = f"👋 Привет, {user_name}!\n\n📅 Ваши записи:\n\n"
            for apt in user_appointments:
                response_text += f"• {apt['service_name']} - {apt['date']} в {apt['time']}\n"
            
            # Добавляем кнопку "Записаться еще"
            available_appointments = await get_available_appointments()
            if available_appointments:
                response_text += "\n💡 Хотите записаться еще?"
                
                buttons = []
                for apt in available_appointments:
                    button_text = f"{apt['service_name']} - {apt['date']} {apt['time']}"
                    button = CallbackButton(text=button_text, payload=f"book_{apt['id']}")
                    buttons.append([button])
                
                # Создаем клавиатуру
                keyboard_payload = InlineKeyboardAttachmentRequestPayload(buttons=buttons)
                keyboard_attachment = InlineKeyboardAttachmentRequest(payload=keyboard_payload)
                
                # Отправляем сообщение с клавиатурой
                message_body = max_client.NewMessageBody(
                    text=response_text,
                    attachments=[keyboard_attachment]
                )
                messages_api.send_message(message_body, user_id=user_id)
            else:
                response_text += "\n😔 К сожалению, новых свободных записей нет."
                send_message_to_user(response_text, user_id)
        else:
            # У пользователя нет записей - показываем доступные
            appointments = await get_available_appointments()
            if appointments:
                send_appointments_keyboard(
                    f"👋 Привет, {user_name}! Я могу помочь вам записаться. Выберите удобное время:",
                    user_id,
                    appointments
                )
            else:
                send_message_to_user("😔 К сожалению, свободных записей нет. Попробуйте позже.", user_id)

async def handle_callback(user_id: int, user_name: str, payload: str, callback_id: str):
    """Обработка callback от кнопок"""
    logger.info(f"Callback от {user_name} (ID: {user_id}): {payload}")
    
    if payload.startswith('book_'):
        appointment_id = int(payload.split('_')[1])
        
        # Бронируем запись
        booked_info = await book_appointment(appointment_id, str(user_id), user_name)
        
        if booked_info:
            success_text = f"✅ Спасибо за запись!\n\n📋 Детали:\n• Услуга: {booked_info['service']}\n• Дата: {booked_info['date']}\n• Время: {booked_info['time']}\n\n📞 Мы свяжемся с вами!"
            send_message_to_user(success_text, user_id)
            
            # Уведомляем админа
            if ADMIN_ID:
                admin_text = f"🎉 Новая запись!\n\n📋 Детали:\n• Услуга: {booked_info['service']}\n• Дата: {booked_info['date']}\n• Время: {booked_info['time']}\n• Клиент: {user_name} (ID: {user_id})"
                send_message_to_user(admin_text, int(ADMIN_ID))
        else:
            send_message_to_user("❌ Не удалось записаться. Возможно, это время уже занято. Попробуйте выбрать другое время.", user_id)
        
        # Подтверждаем callback
        try:
            messages_api.answer_on_callback(callback_id, max_client.CallbackAnswer())
        except Exception as e:
            logger.error(f"Ошибка подтверждения callback: {e}")
