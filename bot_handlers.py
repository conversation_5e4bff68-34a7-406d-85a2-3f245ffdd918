#!/usr/bin/env python3
"""
Обработчики сообщений бота
"""

import os
import logging
import json
from dotenv import load_dotenv
from max_client import Configuration, ApiClient, MessagesApi, NewMessageBody, CallbackButton, \
    Intent, InlineKeyboardAttachmentRequest, InlineKeyboardAttachmentRequestPayload, \
    MessageCreatedUpdate, MessageCallbackUpdate
from database import (
    add_appointment_to_db,
    load_appointments_from_db,
    get_available_appointments,
    book_appointment,
    get_user_appointments,
    delete_appointment
)

load_dotenv()

logger = logging.getLogger(__name__)

# Конфигурация
MAX_BOT_TOKEN = os.getenv('MAX_BOT_TOKEN')
ADMIN_ID = os.getenv('ADMIN_ID')

# Max API клиент
configuration = Configuration()
configuration.api_key['access_token'] = MAX_BOT_TOKEN
api_client = ApiClient(configuration)
messages_api = MessagesApi(api_client)

def add_buttons_to_message_body(message_body, buttons):
    """Добавить кнопки к сообщению"""
    message_body.attachments = [InlineKeyboardAttachmentRequest(InlineKeyboardAttachmentRequestPayload(buttons))]
    return message_body

def send_message_to_user(text: str, chat_id: int, buttons=None):
    """Отправить сообщение пользователю"""
    try:
        message_body = NewMessageBody(text=text)
        if buttons:
            message_body = add_buttons_to_message_body(message_body, buttons)

        result = messages_api.send_message(message_body, chat_id=chat_id)
        logger.info(f"Сообщение отправлено в чат {chat_id}")
        return result
    except Exception as e:
        logger.error(f"Ошибка отправки сообщения: {e}")
        return None

def get_main_menu_buttons():
    """Получить кнопки главного меню"""
    return [
        [CallbackButton("📅 Мои записи", "my_appointments", intent=Intent.POSITIVE)],
        [CallbackButton("🛠️ Услуги", "show_services", intent=Intent.POSITIVE)],
        [CallbackButton("❓ Помощь", "show_help", intent=Intent.POSITIVE)]
    ]

def get_admin_menu_buttons():
    """Получить кнопки админского меню"""
    return [
        [CallbackButton("📋 Все записи", "admin_all_appointments", intent=Intent.POSITIVE)],
        [CallbackButton("➕ Добавить запись", "admin_add_appointment", intent=Intent.POSITIVE)],
        [CallbackButton("🛠️ Услуги", "show_services", intent=Intent.POSITIVE)],
        [CallbackButton("❓ Помощь", "show_help", intent=Intent.POSITIVE)]
    ]

def send_welcome_menu(chat_id: int, user_name: str, is_admin: bool = False):
    """Отправить приветствие и главное меню"""
    try:
        if is_admin:
            welcome_text = f"👋 Привет, {user_name}!\n\n🔧 Администратор\n\nВыберите действие:"
            buttons = get_admin_menu_buttons()
        else:
            welcome_text = f"👋 Привет, {user_name}!\n\n📋 Выберите действие:"
            buttons = get_main_menu_buttons()

        return send_message_to_user(welcome_text, chat_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка отправки приветствия: {e}")
        return None

def send_appointments_keyboard(text: str, user_id: int, appointments: list):
    """Отправить клавиатуру с записями"""
    try:
        if not appointments:
            send_message_to_user("😔 К сожалению, свободных записей нет.", user_id)
            return

        # Создаем кнопки
        buttons = []
        for apt in appointments:
            button_text = f"{apt['service_name']} - {apt['date']} {apt['time']}"
            button = CallbackButton(text=button_text, payload=f"book_{apt['id']}")
            buttons.append([button])

        # Добавляем кнопку "Назад в меню"
        buttons.append([CallbackButton("🔙 Главное меню", "main_menu")])

        send_message_to_user(text, user_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка отправки клавиатуры: {e}")

async def handle_message(user_id: int, user_name: str, message_text: str):
    """Обработка текстового сообщения"""
    logger.info(f"Сообщение от {user_name} (ID: {user_id}): {message_text}")

    # Очищаем текст от лишних символов
    clean_text = message_text.strip()

    # Проверяем, является ли пользователь админом
    is_admin = (str(user_id) == ADMIN_ID)

    # Команда /start для всех пользователей
    if clean_text == "/start":
        send_welcome_menu(user_id, user_name, is_admin)
        return

    # Админские команды
    if is_admin:
        if clean_text.startswith("/add_appointment"):
            parts = clean_text.split(" ", 3)
            if len(parts) == 4:
                service_name = parts[1]
                appt_date = parts[2]
                appt_time = parts[3]
                appt_id = await add_appointment_to_db(service_name, appt_date, appt_time)
                if appt_id:
                    send_message_to_user(f"✅ Запись '{service_name}' на {appt_date} в {appt_time} добавлена (ID: {appt_id}).", user_id)
                else:
                    send_message_to_user("❌ Ошибка при добавлении записи. Возможно, такая запись уже существует.", user_id)
            else:
                send_message_to_user("📖 Использование: /add_appointment <услуга> <дата YYYY-MM-DD> <время HH:MM:SS>", user_id)
        
        elif clean_text == "/list_appointments":
            all_appointments = await load_appointments_from_db()
            if all_appointments:
                response_text = "📋 Все записи:\n\n"
                for appt in all_appointments:
                    status = "🔴 Забронировано" if appt['is_booked'] else "🟢 Свободно"
                    booked_by = f" - {appt['booked_by']}" if appt['is_booked'] and appt['booked_by'] else ""
                    response_text += f"ID {appt['id']}: {appt['service_name']} {appt['date']} в {appt['time']} {status}{booked_by}\n"
            else:
                response_text = "📭 Нет записей в базе данных."
            send_message_to_user(response_text, user_id)
        
        elif clean_text.startswith("/delete_appointment"):
            parts = clean_text.split(" ", 1)
            if len(parts) == 2:
                try:
                    appointment_id = int(parts[1])
                    if await delete_appointment(appointment_id):
                        send_message_to_user(f"✅ Запись ID {appointment_id} удалена.", user_id)
                    else:
                        send_message_to_user(f"❌ Запись ID {appointment_id} не найдена.", user_id)
                except ValueError:
                    send_message_to_user("❌ Неверный ID записи. Используйте: /delete_appointment <ID>", user_id)
            else:
                send_message_to_user("📖 Использование: /delete_appointment <ID>", user_id)
        
        else:
            # Для админа показываем помощь по командам
            admin_help = """🔧 Команды администратора:

/start - главное меню
/add_appointment <услуга> <дата> <время>
Пример: /add_appointment Урок_английского 2025-07-15 10:00:00

/list_appointments - показать все записи
/delete_appointment <ID> - удалить запись

Для обычных пользователей бот показывает главное меню."""
            send_message_to_user(admin_help, user_id)

    else:  # Обычные пользователи
        # Для обычных пользователей любое сообщение кроме /start показывает "Выберите пункты меню"
        response_text = f"📋 Выберите пункты меню:"
        buttons = get_main_menu_buttons()
        send_message_to_user(response_text, user_id, buttons)

async def handle_callback(user_id: int, user_name: str, payload: str, callback_id: str):
    """Обработка callback от кнопок"""
    logger.info(f"Callback от {user_name} (ID: {user_id}): {payload}")

    is_admin = (str(user_id) == ADMIN_ID)

    try:
        if payload == "main_menu":
            # Возврат в главное меню
            send_welcome_menu(user_id, user_name, is_admin)

        elif payload == "my_appointments":
            # Показать записи пользователя
            await show_user_appointments(user_id, user_name)

        elif payload == "show_services":
            # Показать список услуг для записи
            await show_services_menu(user_id, user_name)

        elif payload == "show_help":
            # Показать помощь
            show_help_menu(user_id, user_name, is_admin)

        elif payload == "admin_all_appointments":
            # Админ: показать все записи
            await show_admin_appointments(user_id)

        elif payload == "admin_add_appointment":
            # Админ: инструкция по добавлению записи
            help_text = """➕ Добавление записи

Используйте команду:
/add_appointment <услуга> <дата> <время>

Пример:
/add_appointment Урок_английского 2025-07-15 10:00:00

Формат даты: YYYY-MM-DD
Формат времени: HH:MM:SS"""

            buttons = [[CallbackButton("🔙 Главное меню", "main_menu", intent=Intent.POSITIVE)]]
            send_message_to_user(help_text, user_id, buttons)

        elif payload.startswith('book_'):
            # Бронирование записи
            appointment_id = int(payload.split('_')[1])

            # Бронируем запись
            booked_info = await book_appointment(appointment_id, str(user_id), user_name)

            if booked_info:
                success_text = f"✅ Спасибо за запись!\n\n📋 Детали:\n• Услуга: {booked_info['service']}\n• Дата: {booked_info['date']}\n• Время: {booked_info['time']}\n\n📞 Мы свяжемся с вами!"

                buttons = [[CallbackButton("🔙 Главное меню", "main_menu")]]
                send_message_with_keyboard(success_text, user_id, buttons)

                # Уведомляем админа
                if ADMIN_ID:
                    admin_text = f"🎉 Новая запись!\n\n📋 Детали:\n• Услуга: {booked_info['service']}\n• Дата: {booked_info['date']}\n• Время: {booked_info['time']}\n• Клиент: {user_name} (ID: {user_id})"
                    send_message_to_user(admin_text, int(ADMIN_ID))
            else:
                error_text = "❌ Не удалось записаться. Возможно, это время уже занято. Попробуйте выбрать другое время."
                buttons = [[CallbackButton("🔙 Главное меню", "main_menu")]]
                send_message_with_keyboard(error_text, user_id, buttons)

        # Подтверждаем callback (пока закомментируем, так как нет импорта CallbackAnswer)
        # messages_api.answer_on_callback(callback_id, CallbackAnswer())

    except Exception as e:
        logger.error(f"Ошибка обработки callback: {e}")
        # В случае ошибки возвращаем в главное меню
        send_welcome_menu(user_id, user_name, is_admin)

        # try:
        #     messages_api.answer_on_callback(callback_id, CallbackAnswer())
        # except:
        #     pass

async def show_user_appointments(user_id: int, user_name: str):
    """Показать записи пользователя"""
    try:
        user_appointments = await get_user_appointments(str(user_id))

        if user_appointments:
            response_text = f"📅 Ваши записи, {user_name}:\n\n"

            # Разделяем на активные и прошедшие
            from datetime import datetime, date
            today = date.today()

            active_appointments = []
            past_appointments = []

            for apt in user_appointments:
                apt_date = apt['date']
                if isinstance(apt_date, str):
                    apt_date = datetime.strptime(apt_date, '%Y-%m-%d').date()

                if apt_date >= today:
                    active_appointments.append(apt)
                else:
                    past_appointments.append(apt)

            if active_appointments:
                response_text += "🟢 Активные записи:\n"
                for apt in active_appointments:
                    response_text += f"• {apt['service_name']} - {apt['date']} в {apt['time']}\n"
                response_text += "\n"

            if past_appointments:
                response_text += "🔘 Прошедшие записи:\n"
                for apt in past_appointments:
                    response_text += f"• {apt['service_name']} - {apt['date']} в {apt['time']}\n"
        else:
            response_text = f"📅 У вас пока нет записей, {user_name}.\n\n💡 Выберите услугу для записи!"

        buttons = [
            [CallbackButton("🛠️ Записаться на услугу", "show_services")],
            [CallbackButton("🔙 Главное меню", "main_menu")]
        ]
        send_message_with_keyboard(response_text, user_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка показа записей пользователя: {e}")

async def show_services_menu(user_id: int, user_name: str):
    """Показать список услуг для записи"""
    try:
        appointments = await get_available_appointments()

        if appointments:
            response_text = f"🛠️ Доступные услуги для записи:\n\nВыберите удобное время:"

            # Создаем кнопки для записи
            buttons = []
            for apt in appointments:
                button_text = f"{apt['service_name']} - {apt['date']} {apt['time']}"
                button = CallbackButton(text=button_text, payload=f"book_{apt['id']}")
                buttons.append([button])

            # Добавляем кнопку "Назад в меню"
            buttons.append([CallbackButton("🔙 Главное меню", "main_menu")])

            send_message_with_keyboard(response_text, user_id, buttons)
        else:
            response_text = "😔 К сожалению, свободных записей нет.\n\n💡 Попробуйте позже или свяжитесь с нами!"
            buttons = [[CallbackButton("🔙 Главное меню", "main_menu")]]
            send_message_with_keyboard(response_text, user_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка показа услуг: {e}")

def show_help_menu(user_id: int, user_name: str, is_admin: bool = False):
    """Показать помощь"""
    try:
        if is_admin:
            help_text = """❓ Помощь для администратора

🔧 Команды:
• /start - главное меню
• /add_appointment <услуга> <дата> <время> - добавить запись
• /list_appointments - показать все записи
• /delete_appointment <ID> - удалить запись

📋 Пример добавления:
/add_appointment Урок_английского 2025-07-15 10:00:00

💡 Через меню вы можете просматривать все записи и получать уведомления о новых бронированиях."""
        else:
            help_text = """❓ Помощь для клиентов

🎯 Как записаться:
1. Выберите "🛠️ Услуги" в главном меню
2. Выберите удобное время из списка
3. Подтвердите запись

📅 Ваши записи:
• В разделе "📅 Мои записи" вы увидите активные и прошедшие записи

📞 Связь:
• После записи мы свяжемся с вами для подтверждения

💡 Команда /start всегда вернет вас в главное меню."""

        buttons = [[CallbackButton("🔙 Главное меню", "main_menu", intent=Intent.POSITIVE)]]
        send_message_to_user(help_text, user_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка показа помощи: {e}")

async def show_admin_appointments(user_id: int):
    """Показать все записи для админа"""
    try:
        all_appointments = await load_appointments_from_db()

        if all_appointments:
            response_text = "📋 Все записи в системе:\n\n"
            for appt in all_appointments:
                status = "🔴 Забронировано" if appt['is_booked'] else "🟢 Свободно"
                booked_info = f" - {appt['booked_by']}" if appt['is_booked'] and appt['booked_by'] else ""
                response_text += f"ID {appt['id']}: {appt['service_name']}\n📅 {appt['date']} ⏰ {appt['time']}\n{status}{booked_info}\n\n"
        else:
            response_text = "📭 В системе пока нет записей.\n\n💡 Используйте /add_appointment для добавления."

        buttons = [
            [CallbackButton("➕ Добавить запись", "admin_add_appointment")],
            [CallbackButton("🔙 Главное меню", "main_menu")]
        ]
        send_message_with_keyboard(response_text, user_id, buttons)

    except Exception as e:
        logger.error(f"Ошибка показа записей админа: {e}")
